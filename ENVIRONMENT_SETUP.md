# GameFlex Mobile Environment Setup

## Overview

GameFlex Mobile now supports both development and production build configurations with automatic environment-aware URL selection.

## ✅ What's Been Implemented

### 1. Environment-Aware Configuration
- **Development Mode**: Uses local backend URLs (localhost/********)
- **Production Mode**: Uses production URLs (https://api.gameflex.io)
- Automatic platform-specific URL selection for Android emulator compatibility

### 2. Build Scripts
- **`build-dev.ps1`**: Development builds with local backend
- **`build-prod.ps1`**: Production builds with gameflex.io backend
- **`run-dev.ps1`**: Run in development mode with hot reload
- **`run-prod.ps1`**: Run in production mode (with safety confirmation)

### 3. Android & iOS Build Configurations
- **Development**: `com.example.gameflex_mobile.dev` with "GameFlex Dev" name
- **Production**: `com.example.gameflex_mobile` with "GameFlex" name
- Separate app icons and configurations for each environment
- iOS uses xcconfig files and schemes for environment separation

### 4. Automated Testing
- **`test/environment_test.dart`**: Tests development configuration
- **`test/production_environment_test.dart`**: Tests production configuration
- Validates URL transformations and environment detection

## 🚀 Quick Usage

### Development
```powershell
# Run development (Windows)
.\run-dev.ps1

# Run development (Android)
.\run-dev.ps1 -Platform android

# Run development (iOS)
.\run-dev.ps1 -Platform ios

# Build development
.\build-dev.ps1 -Platform android
```

### Production
```powershell
# Run production (with confirmation)
.\run-prod.ps1 -Platform android

# Run production (iOS)
.\run-prod.ps1 -Platform ios

# Build production
.\build-prod.ps1 -Platform android
```

## 🔧 Technical Details

### Environment Detection
The app uses `bool.fromEnvironment('PRODUCTION')` to detect the build environment:
- Development: `PRODUCTION=false` (default)
- Production: `PRODUCTION=true` (set via `--dart-define=PRODUCTION=true`)

### URL Configuration
- **Development**: 
  - Windows/Web: `http://localhost:8090`
  - Android: `http://********:8090` (emulator compatibility)
- **Production**: `http://api.gameflex.io:8000`

### URL Transformation
The app uses environment-aware URL construction for all media and API endpoints:
- **`SupabaseService.supabaseUrl`**: Returns environment-appropriate base URL
- **`SupabaseService.transformUrl()`**: Transforms existing URLs based on environment
- **`MediaUrlHelper.constructMediaUrl()`**: Constructs media URLs using environment-aware base URL

URL handling by environment:
- **Development**: Uses localhost/******** for Android compatibility
- **Production**: Uses api.gameflex.io:8000 for all endpoints

## 📱 Platform Support

### Windows
- ✅ Development builds
- ✅ Production builds
- ✅ Debug and release modes

### Android
- ✅ Development flavor with separate package ID
- ✅ Production flavor
- ✅ Emulator-compatible URLs
- ✅ Separate app names and icons

### iOS
- ✅ Development configuration with separate bundle ID
- ✅ Production configuration
- ✅ Separate app names and display names
- ✅ Environment-specific xcconfig files

### Web
- ✅ Development builds
- ✅ Production builds
- ✅ Runs on port 3001 to avoid conflicts

## 🧪 Testing

### Run Environment Tests
```powershell
# Test development configuration
flutter test test/environment_test.dart

# Test production configuration
flutter test test/production_environment_test.dart --dart-define=PRODUCTION=true
```

## 📋 Next Steps

### For Production Deployment
1. **Update Production Anonymous Key**: Replace the TODO in `SupabaseService.supabaseAnonKey` with your actual production Supabase anonymous key
2. **Configure Production Backend**: Ensure https://api.gameflex.io is accessible and properly configured
3. **Code Signing**: Set up proper code signing for Android/Windows production builds
4. **Testing**: Thoroughly test production builds before deployment

### Optional Enhancements
- Add staging environment support
- Implement feature flags
- Add build number automation
- Set up CI/CD pipeline integration

## 🔒 Security Notes

- Production builds use release mode only for optimal performance
- Development builds include debug logging for troubleshooting
- Separate package IDs prevent accidental production data access during development
- Environment-specific anonymous keys ensure proper backend access control

## 📚 Documentation

- **`BUILD_GUIDE.md`**: Detailed build instructions and examples
- **`IOS_SETUP_GUIDE.md`**: iOS-specific configuration and troubleshooting
- **Script Help**: Run any script with `-Help` for usage information
- **Flutter Documentation**: Standard Flutter build commands still work alongside these scripts
