import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../lib/services/upload_service.dart';

void main() {
  group('Media Upload Tests', () {
    late SupabaseClient client;

    setUpAll(() async {
      // Initialize Supabase client
      await Supabase.initialize(
        url: 'http://localhost:8090',
        anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
      );
      client = Supabase.instance.client;
    });

    test('Test media table structure and post creation', () async {
      print('🧪 Testing new media table structure...');

      try {
        // Authenticate as test user
        final authResponse = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'devpassword123',
        );

        expect(authResponse.user, isNotNull);
        print('✅ Authenticated as: ${authResponse.user!.email}');

        // Create a test image file (9:16 black JPEG - 180x320 pixels)
        final testImageBytes = _createBlackJPEG();
        final tempDir = Directory.systemTemp;
        final testImageFile = File('${tempDir.path}/test_media_image.jpg');
        await testImageFile.writeAsBytes(testImageBytes);

        print('📸 Created test image file: ${testImageFile.path}');

        // Test the new upload service
        final uploadService = UploadService();
        final success = await uploadService.createPost(
          imageFile: testImageFile,
          content: 'Test post with new media table structure',
        );

        expect(success, isTrue);
        print('✅ Post created successfully with new media structure');

        // Verify media record was created
        final mediaRecords = await client.from('media').select().limit(1);
        expect(mediaRecords, isNotEmpty);
        print('✅ Media record found: ${mediaRecords.first['id']}');

        // Verify post record was created with media_id reference
        final posts = await client
            .from('posts')
            .select('*, media(*)')
            .eq('content', 'Test post with new media table structure')
            .limit(1);

        expect(posts, isNotEmpty);
        expect(posts.first['media_id'], isNotNull);
        expect(posts.first['media'], isNotNull);
        print('✅ Post found with media relationship: ${posts.first['id']}');

        // Test URL construction
        final mediaData = posts.first['media'];
        final constructedUrl = MediaUrlHelper.constructMediaUrl(mediaData);
        print('🔗 Constructed URL: $constructedUrl');
        expect(constructedUrl, contains('http://localhost:8090'));
        expect(constructedUrl, contains('storage/v1/object/public/media'));

        // Clean up test file
        if (await testImageFile.exists()) {
          await testImageFile.delete();
        }

        print('🎉 All media table tests passed!');
      } catch (e) {
        print('❌ Test failed: $e');
        rethrow;
      }
    });
  });
}

/// Create a minimal black JPEG image (9:16 aspect ratio)
Uint8List _createBlackJPEG() {
  // This is a minimal JPEG header for a 180x320 black image
  return Uint8List.fromList([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x01, 0x40,
    0x00, 0xB4, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF,
    0xD9
  ]);
}
