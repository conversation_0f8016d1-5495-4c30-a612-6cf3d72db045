import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/upload_service.dart';

void main() {
  group('Production Media URL Tests', () {
    test('MediaUrlHelper should use production URL when PRODUCTION=true', () {
      // This test should be run with: flutter test --dart-define=PRODUCTION=true
      const isProduction = bool.fromEnvironment(
        'PRODUCTION',
        defaultValue: false,
      );

      if (!isProduction) {
        // Skip this test if not running in production mode
        return;
      }

      // Test media data
      final testMedia = {
        'type': 'image',
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      // Test URL construction without explicit baseUrl (should use environment-aware URL)
      final constructedUrl = MediaUrlHelper.constructMediaUrl(testMedia);

      // Should use production URL
      expect(
        constructedUrl.contains('api.gameflex.io:8000'),
        true,
        reason: 'Production should use api.gameflex.io:8000, got: $constructedUrl',
      );

      // Should not use local URLs
      expect(
        constructedUrl.contains('localhost') || constructedUrl.contains('********'),
        false,
        reason: 'Production should not use local URLs, got: $constructedUrl',
      );

      // Should have correct path structure
      expect(
        constructedUrl,
        equals('http://api.gameflex.io:8000/storage/v1/object/public/media/user/user-123/test_image.jpg'),
        reason: 'URL should match expected production format',
      );
    });

    test('MediaUrlHelper should respect explicit baseUrl parameter', () {
      final testMedia = {
        'type': 'image',
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      // Test with explicit production baseUrl
      final explicitUrl = MediaUrlHelper.constructMediaUrl(
        testMedia,
        baseUrl: 'http://api.gameflex.io:8000',
      );

      expect(
        explicitUrl,
        equals('http://api.gameflex.io:8000/storage/v1/object/public/media/user/user-123/test_image.jpg'),
        reason: 'Explicit baseUrl should be used correctly',
      );
    });
  });
}
