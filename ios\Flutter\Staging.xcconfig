#include "Generated.xcconfig"

// Staging configuration for GameFlex Mobile
PRODUCT_BUNDLE_IDENTIFIER = com.example.gameflexMobile.staging
BUNDLE_DISPLAY_NAME = GameFlex Staging
BUNDLE_NAME = GameFlex Staging

// Staging-specific settings
FLUTTER_BUILD_MODE = release
DART_DEFINES = STAGING=true

// Code signing (adjust as needed for your distribution team)
CODE_SIGN_IDENTITY = iPhone Distribution
DEVELOPMENT_TEAM =

// Deployment target
IPHONEOS_DEPLOYMENT_TARGET = 12.0

// Staging optimizations
ENABLE_BITCODE = NO
VALIDATE_PRODUCT = YES
