import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/channel_model.dart';
import '../providers/channels_provider.dart';
import '../theme/app_theme.dart';

class ChannelCard extends StatelessWidget {
  final ChannelModel channel;
  final VoidCallback onTap;

  const ChannelCard({super.key, required this.channel, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _getChannelColor(
                channel.name,
              ).withValues(alpha: 230), // 0.9 opacity
              _getChannelColor(
                channel.name,
              ).withValues(alpha: 179), // 0.7 opacity
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 77), // 0.3 opacity
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main game logo/image
            Center(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: _buildChannelImage(),
                ),
              ),
            ),

            // Overlay with channel name and member count
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 179), // 0.7 opacity
                      Colors.black.withValues(alpha: 230), // 0.9 opacity
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      channel.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${channel.memberCount} members',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 12,
                        shadows: const [
                          Shadow(
                            color: Colors.black,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Join/Leave button in top right
            Positioned(
              top: 8,
              right: 8,
              child: Consumer<ChannelsProvider>(
                builder: (context, channelsProvider, child) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap:
                            () => _handleJoinLeave(context, channelsProvider),
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          child: Text(
                            channel.isUserMember ? 'Leave' : 'Join',
                            style: TextStyle(
                              color:
                                  channel.isUserMember
                                      ? Colors.orange.shade300
                                      : AppColors.gfGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChannelImage() {
    // For now, use a large icon with the channel color as background
    // In the future, this could load actual game logos from assets or network
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getChannelColor(
              channel.name,
            ).withValues(alpha: 128), // 0.5 opacity
            _getChannelColor(
              channel.name,
            ).withValues(alpha: 204), // 0.8 opacity
          ],
        ),
      ),
      child: Center(
        child: Icon(
          _getChannelIcon(channel.name),
          size: 80,
          color: Colors.white.withValues(alpha: 230), // 0.9 opacity
          shadows: const [
            Shadow(color: Colors.black, blurRadius: 4, offset: Offset(2, 2)),
          ],
        ),
      ),
    );
  }

  void _handleJoinLeave(
    BuildContext context,
    ChannelsProvider channelsProvider,
  ) async {
    bool success;

    if (channel.isUserMember) {
      success = await channelsProvider.leaveChannel(channel.id);
      if (success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Left ${channel.name}'),
              backgroundColor: Colors.orange.shade700,
            ),
          );
        }
      }
    } else {
      success = await channelsProvider.joinChannel(channel.id);
      if (success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Joined ${channel.name}'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        }
      }
    }

    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to ${channel.isUserMember ? 'leave' : 'join'} ${channel.name}',
          ),
          backgroundColor: Colors.red.shade700,
        ),
      );
    }
  }

  Color _getChannelColor(String channelName) {
    final name = channelName.toLowerCase();

    // Define specific colors for known game channels
    if (name.contains('minecraft')) {
      return const Color(0xFF4CAF50); // Minecraft Green
    } else if (name.contains('fortnite')) {
      return const Color(0xFF8E44AD); // Fortnite Purple
    } else if (name.contains('valorant')) {
      return const Color(0xFFE74C3C); // Valorant Red
    } else if (name.contains('league') || name.contains('lol')) {
      return const Color(0xFF3498DB); // League Blue
    } else if (name.contains('apex')) {
      return const Color(0xFFE67E22); // Apex Orange
    } else if (name.contains('gaming') || name.contains('general')) {
      return AppColors.gfGreen;
    } else if (name.contains('call of duty') || name.contains('cod')) {
      return const Color(0xFF2C3E50); // COD Dark Blue
    } else if (name.contains('overwatch')) {
      return const Color(0xFFF39C12); // Overwatch Orange
    } else if (name.contains('rocket league')) {
      return const Color(0xFF9B59B6); // Rocket League Purple
    } else if (name.contains('counter strike') || name.contains('cs')) {
      return const Color(0xFF34495E); // CS Grey
    }

    // Fallback to vibrant hash-based color for unknown channels
    final hash = channelName.hashCode;
    final colors = [
      const Color(0xFF4CAF50), // Green
      const Color(0xFF3498DB), // Blue
      const Color(0xFF9B59B6), // Purple
      const Color(0xFFE74C3C), // Red
      const Color(0xFFE67E22), // Orange
      const Color(0xFF1ABC9C), // Turquoise
      const Color(0xFFF39C12), // Yellow
      const Color(0xFF2ECC71), // Emerald
    ];

    return colors[hash.abs() % colors.length];
  }

  IconData _getChannelIcon(String channelName) {
    final name = channelName.toLowerCase();

    if (name.contains('gaming') || name.contains('game')) {
      return Icons.sports_esports;
    } else if (name.contains('mobile')) {
      return Icons.phone_android;
    } else if (name.contains('competitive') || name.contains('esports')) {
      return Icons.emoji_events;
    } else if (name.contains('indie')) {
      return Icons.lightbulb_outline;
    } else if (name.contains('development') || name.contains('dev')) {
      return Icons.code;
    } else if (name.contains('minecraft')) {
      return Icons.view_module;
    } else if (name.contains('fortnite')) {
      return Icons.architecture;
    } else if (name.contains('valorant')) {
      return Icons.gps_fixed;
    } else if (name.contains('league') || name.contains('lol')) {
      return Icons.shield;
    } else if (name.contains('apex')) {
      return Icons.flight_takeoff;
    } else {
      return Icons.sports_esports; // Default gaming icon
    }
  }
}
