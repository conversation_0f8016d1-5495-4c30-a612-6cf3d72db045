import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// ignore: depend_on_referenced_packages, uri_does_not_exist
import 'package:video_player/video_player.dart';
import '../main.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final bool autoPlay;
  final bool isVisible;

  const VideoPlayerWidget({
    super.key,
    required this.videoUrl,
    this.autoPlay = true,
    this.isVisible = true,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver, RouteAware {
  // ignore: undefined_class
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  bool _isPlaying = false;
  final bool _isMuted = true;
  bool _showControls = false;
  late AnimationController _animationController;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    if (kDebugMode) {
      print('VideoPlayerWidget: Initialized for URL: ${widget.videoUrl}');
      print('VideoPlayerWidget: AutoPlay: ${widget.autoPlay}, Visible: ${widget.isVisible}');
    }

    // Initialize video player
    _initializeVideoPlayer();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Subscribe to route observer
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      MyApp.routeObserver.subscribe(this, route);
    }
  }

  void _initializeVideoPlayer() async {
    try {
      if (widget.videoUrl.isNotEmpty &&
          !widget.videoUrl.startsWith('debug') &&
          !widget.videoUrl.startsWith('test')) {

        // Dispose any existing controller first
        _disposeController();

        // ignore: undefined_identifier
        _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

        await _controller!.initialize();

        if (mounted) {
          setState(() {
            _isInitialized = true;
            _hasError = false;
          });

          // Set up video properties
          _controller!.setLooping(true);
          _controller!.setVolume(_isMuted ? 0.0 : 1.0);

          // Auto-play if conditions are met
          if (widget.autoPlay && widget.isVisible) {
            _startPlaying();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('VideoPlayerWidget: Error initializing video: $e');
      }
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }



  void _startPlaying() {
    setState(() {
      _isPlaying = true;
    });

    if (_controller != null && _isInitialized) {
      _controller!.play();
    } else {
      _animationController.repeat();
    }

    if (kDebugMode) {
      print('VideoPlayer: Started playing video - ${widget.videoUrl}');
    }
  }

  void _stopPlaying() {
    if (mounted) {
      setState(() {
        _isPlaying = false;
      });
    }

    if (_controller != null && _isInitialized) {
      _controller!.pause();
    } else {
      _animationController.stop();
    }

    if (kDebugMode) {
      print('VideoPlayer: Stopped playing video');
    }
  }

  void _handleVisibilityChange() {
    if (kDebugMode) {
      print('VideoPlayerWidget: Visibility changed to ${widget.isVisible}');
    }

    if (widget.isVisible && widget.autoPlay) {
      _startPlaying();
    } else {
      // When not visible, force stop to prevent buffer issues
      _forceStopVideo();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _stopPlaying();
    } else {
      _startPlaying();
    }

    // Show controls briefly when toggling
    _showControls = true;
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }



  @override
  void didUpdateWidget(VideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle visibility changes for autoplay
    if (widget.isVisible != oldWidget.isVisible) {
      _handleVisibilityChange();
    }

    // Handle URL changes
    if (widget.videoUrl != oldWidget.videoUrl) {
      _disposeController();
      _initializeVideoPlayer();
    }
  }

  void _disposeController() {
    if (_controller != null) {
      if (kDebugMode) {
        print('VideoPlayerWidget: Disposing video controller');
      }

      try {
        // Force stop everything before disposal
        _controller!.pause();
        _controller!.setVolume(0.0);
        _controller!.seekTo(Duration.zero);

        // Small delay to ensure operations complete
        Future.delayed(const Duration(milliseconds: 50), () {
          _controller?.dispose();
        });

        _controller = null;
      } catch (e) {
        if (kDebugMode) {
          print('VideoPlayerWidget: Error during controller disposal: $e');
        }
        _controller = null;
      }
    }

    _isInitialized = false;
    _hasError = false;
    _isPlaying = false;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (kDebugMode) {
      print('VideoPlayerWidget: App lifecycle state changed to $state');
    }

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // App is going to background or being paused - force stop video
        _forceStopVideo();
        break;
      case AppLifecycleState.resumed:
        // App is coming back to foreground - only resume if visible and autoplay
        if (widget.isVisible && widget.autoPlay) {
          _startPlaying();
        }
        break;
      case AppLifecycleState.hidden:
        // App is hidden - force stop video
        _forceStopVideo();
        break;
    }
  }

  // RouteAware methods to detect navigation changes
  @override
  void didPushNext() {
    // User navigated to another screen (like comments) - completely stop video
    if (kDebugMode) {
      print('VideoPlayerWidget: Route pushed (navigated away) - stopping video completely');
    }
    _forceStopVideo();
  }

  void _forceStopVideo() {
    // Aggressively stop all video operations
    _stopPlaying();

    if (_controller != null) {
      try {
        if (_isInitialized) {
          _controller!.pause();
          _controller!.seekTo(Duration.zero); // Reset to beginning
          _controller!.setVolume(0.0);
        }

        // Completely dispose the controller to stop all rendering
        _disposeController();

        if (kDebugMode) {
          print('VideoPlayerWidget: Video forcefully stopped and controller disposed');
        }
      } catch (e) {
        if (kDebugMode) {
          print('VideoPlayerWidget: Error during force stop: $e');
        }
        // Force disposal even if there's an error
        _controller = null;
        _isInitialized = false;
        _isPlaying = false;
      }
    }

    // Also stop the animation controller
    _animationController.stop();
    _animationController.reset();
  }

  @override
  void didPopNext() {
    // User came back from another screen - resume if conditions are met
    if (kDebugMode) {
      print('VideoPlayerWidget: Route popped (navigated back) - checking resume conditions');
    }
    if (widget.isVisible && widget.autoPlay) {
      _startPlaying();
    }
  }

  @override
  void didPush() {
    // This route was pushed
    if (kDebugMode) {
      print('VideoPlayerWidget: This route was pushed');
    }
  }

  @override
  void didPop() {
    // This route was popped
    if (kDebugMode) {
      print('VideoPlayerWidget: This route was popped');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    MyApp.routeObserver.unsubscribe(this);
    _disposeController();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('VideoPlayerWidget: Building widget, isPlaying: $_isPlaying, URL: ${widget.videoUrl}');
    }

    // Aggressive check: if not visible, immediately force stop
    if (!widget.isVisible && _isPlaying) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _forceStopVideo();
      });
    }

    return GestureDetector(
      onTap: _togglePlayPause,
      child: SizedBox.expand(
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Video content area
            if (widget.videoUrl.isNotEmpty && !widget.videoUrl.startsWith('debug') && !widget.videoUrl.startsWith('test'))
              _buildVideoContent()
            else
              _buildFallbackBackground(),

            // Video controls overlay
            _buildVideoOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    // Show actual video if initialized, otherwise show loading/error state
    if (_hasError) {
      return _buildErrorState();
    }

    if (!_isInitialized) {
      return _buildLoadingState();
    }

    // Show actual video player
    if (_controller != null && _isInitialized) {
      return Center(
        child: AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          // ignore: undefined_method
          child: VideoPlayer(_controller!),
        ),
      );
    }

    // Fallback to simulation
    return _buildVideoSimulation();
  }

  Widget _buildLoadingState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading Video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white.withValues(alpha: 179), // 0.7 opacity
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Video',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 179), // 0.7 opacity
                fontSize: 16,
              ),
            ),
            if (kDebugMode && _errorMessage != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _errorMessage!,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 128), // 0.5 opacity
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVideoSimulation() {
    // Fallback simulation for when video player fails
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0 + (_animationController.value * 0.3),
              colors: [
                Colors.grey[800]!,
                Colors.grey[900]!,
                Colors.black,
              ],
              stops: [0.0, 0.7, 1.0],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.videocam,
                  size: 64,
                  color: Colors.white.withValues(alpha: 179), // 0.7 opacity
                ),
                const SizedBox(height: 16),
                Text(
                  'Video Simulation',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 179), // 0.7 opacity
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFallbackBackground() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF2BA5A5), // AppColors.gfTeal
                const Color(0xFF293642), // AppColors.gfDarkBlue
              ],
              transform: GradientRotation(_animationController.value * 2 * 3.14159),
            ),
          ),
          child: _buildVideoOverlay(),
        );
      },
    );
  }

  Widget _buildVideoOverlay() {
    return AnimatedOpacity(
      opacity: _showControls || !_isPlaying ? 1.0 : 0.0, // Completely hide when playing
      duration: const Duration(milliseconds: 300),
      child: Stack(
        children: [
          // Play/Pause button in center
          Center(
            child: AnimatedScale(
              scale: _isPlaying ? 0.0 : 1.0, // Hide when playing
              duration: const Duration(milliseconds: 300),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 179), // 0.7 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.play_arrow,
                  size: 56,
                  color: Colors.white,
                ),
              ),
            ),
          ),

          // Pause indicator (shows briefly when pausing)
          if (_isPlaying && _showControls)
            Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 179), // 0.7 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.pause,
                  size: 56,
                  color: Colors.white,
                ),
              ),
            ),

          // Mute indicator
          if (_isMuted)
            Positioned(
              top: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.volume_off,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
        ],
      ),
    );
  }

}
