import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:gameflex_mobile/services/auth_service.dart';
import 'package:gameflex_mobile/services/user_service.dart';
import 'package:gameflex_mobile/services/posts_service.dart';
import 'package:gameflex_mobile/providers/user_profile_provider.dart';

void main() {
  group('User Profile Integration Tests', () {
    setUpAll(() async {
      // Initialize Supabase for testing
      await SupabaseService.initialize();
    });

    test('UserService can fetch user profile', () async {
      // Test with a known development user
      const testEmail = '<EMAIL>';
      const testPassword = 'devpassword123';

      try {
        // Sign in to get a valid user ID
        final signInResult = await AuthService.instance.signIn(
          email: testEmail,
          password: testPassword,
        );

        expect(signInResult.user, isNotNull);

        final currentUser = signInResult.user;
        expect(currentUser, isNotNull);

        // Test getting user profile
        final userProfile = await UserService.instance.getUserProfile(
          currentUser!.id,
        );
        expect(userProfile, isNotNull);
        expect(userProfile!['id'], equals(currentUser.id));
        expect(userProfile['email'], equals(testEmail));

        print(
          '✅ User profile fetched successfully: ${userProfile['display_name']}',
        );

        // Test getting user stats
        final userStats = await UserService.instance.getUserStats(
          currentUser.id,
        );
        expect(userStats, isNotNull);
        expect(userStats.containsKey('posts'), isTrue);
        expect(userStats.containsKey('followers'), isTrue);
        expect(userStats.containsKey('following'), isTrue);
        expect(userStats.containsKey('likes'), isTrue);

        print('✅ User stats fetched successfully: $userStats');

        // Test getting user posts
        final userPosts = await PostsService.instance.getUserPosts(
          currentUser.id,
        );
        expect(userPosts, isNotNull);

        print('✅ User posts fetched successfully: ${userPosts.length} posts');

        // Clean up
        await AuthService.instance.signOut();
      } catch (e) {
        print('❌ Test failed with error: $e');
        rethrow;
      }
    });

    test('UserProfileProvider loads user data correctly', () async {
      const testEmail = '<EMAIL>';
      const testPassword = 'devpassword123';

      try {
        // Sign in to get a valid user ID
        final signInResult = await AuthService.instance.signIn(
          email: testEmail,
          password: testPassword,
        );

        expect(signInResult.user, isNotNull);

        final currentUser = signInResult.user;
        expect(currentUser, isNotNull);

        // Test UserProfileProvider
        final provider = UserProfileProvider();

        // Initial state
        expect(provider.status, UserProfileStatus.initial);
        expect(provider.userProfile, isNull);
        expect(provider.userPosts, isEmpty);

        // Load user profile
        await provider.loadUserProfile(currentUser!.id);

        // Check loaded state
        expect(provider.status, UserProfileStatus.loaded);
        expect(provider.userProfile, isNotNull);
        expect(provider.userProfile!['id'], equals(currentUser.id));
        expect(provider.userStats, isNotNull);

        print('✅ UserProfileProvider loaded successfully');
        print('   User: ${provider.userProfile!['display_name']}');
        print('   Posts: ${provider.userPosts.length}');
        print('   Stats: ${provider.userStats}');

        // Test refresh
        await provider.refreshProfile();
        expect(provider.status, UserProfileStatus.loaded);

        print('✅ Profile refresh successful');

        // Clean up
        await AuthService.instance.signOut();
      } catch (e) {
        print('❌ UserProfileProvider test failed: $e');
        rethrow;
      }
    });

    test('UserProfileProvider handles invalid user ID', () async {
      final provider = UserProfileProvider();

      // Try to load profile with invalid user ID
      await provider.loadUserProfile('invalid-user-id');

      // Should be in error state
      expect(provider.status, UserProfileStatus.error);
      expect(provider.error, isNotNull);
      expect(provider.userProfile, isNull);

      print('✅ Error handling works correctly: ${provider.error}');
    });
  });
}
