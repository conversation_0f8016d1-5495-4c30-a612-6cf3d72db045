import '../services/supabase_service.dart';

class ChannelModel {
  final String id;
  final String name;
  final String? description;
  final String ownerId;
  final bool isPublic;
  final bool isActive;
  final int memberCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Owner information (from join)
  final String? ownerUsername;
  final String? ownerDisplayName;
  final String? ownerAvatarUrl;

  // User membership status
  final bool isUserMember;
  final String? userRole; // 'owner', 'admin', 'moderator', 'member'

  ChannelModel({
    required this.id,
    required this.name,
    this.description,
    required this.ownerId,
    required this.isPublic,
    required this.isActive,
    required this.memberCount,
    required this.createdAt,
    required this.updatedAt,
    this.ownerUsername,
    this.ownerDisplayName,
    this.ownerAvatarUrl,
    this.isUserMember = false,
    this.userRole,
  });

  factory ChannelModel.fromJson(Map<String, dynamic> json) {
    return ChannelModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      ownerId: json['owner_id'] as String,
      isPublic: json['is_public'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      memberCount: json['member_count'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      ownerUsername: json['owner_username'] as String?,
      ownerDisplayName: json['owner_display_name'] as String?,
      ownerAvatarUrl: SupabaseService.transformUrl(json['owner_avatar_url'] as String?),
      isUserMember: json['is_user_member'] as bool? ?? false,
      userRole: json['user_role'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'owner_id': ownerId,
      'is_public': isPublic,
      'is_active': isActive,
      'member_count': memberCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'owner_username': ownerUsername,
      'owner_display_name': ownerDisplayName,
      'owner_avatar_url': ownerAvatarUrl,
      'is_user_member': isUserMember,
      'user_role': userRole,
    };
  }

  ChannelModel copyWith({
    String? id,
    String? name,
    String? description,
    String? ownerId,
    bool? isPublic,
    bool? isActive,
    int? memberCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? ownerUsername,
    String? ownerDisplayName,
    String? ownerAvatarUrl,
    bool? isUserMember,
    String? userRole,
  }) {
    return ChannelModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      isPublic: isPublic ?? this.isPublic,
      isActive: isActive ?? this.isActive,
      memberCount: memberCount ?? this.memberCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      ownerUsername: ownerUsername ?? this.ownerUsername,
      ownerDisplayName: ownerDisplayName ?? this.ownerDisplayName,
      ownerAvatarUrl: ownerAvatarUrl ?? this.ownerAvatarUrl,
      isUserMember: isUserMember ?? this.isUserMember,
      userRole: userRole ?? this.userRole,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChannelModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChannelModel(id: $id, name: $name, memberCount: $memberCount)';
  }
}
