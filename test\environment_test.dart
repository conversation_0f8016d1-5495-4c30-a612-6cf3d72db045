import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';

void main() {
  group('Environment Configuration Tests', () {
    test('Development environment should use local URLs', () {
      // This test runs in development mode by default (PRODUCTION=false)
      final url = SupabaseService.supabaseUrl;
      
      // Should use localhost or ******** for development
      expect(
        url.contains('localhost') || url.contains('********'),
        true,
        reason: 'Development should use local URLs, got: $url',
      );
      
      // Should not use production URL
      expect(
        url.contains('gameflex.io'),
        false,
        reason: 'Development should not use production URLs, got: $url',
      );
    });

    test('URL transformation should work for development', () {
      // Test various URL transformations for development
      final testUrls = [
        'http://localhost:8090/storage/v1/object/public/uploads/test.jpg',
        'http://gameflex.local:8090/storage/v1/object/public/uploads/test.jpg',
        'http://api.gameflex.local:8090/rest/v1/posts',
      ];

      for (final url in testUrls) {
        final transformed = SupabaseService.transformUrl(url);
        
        // In development, should transform to ********
        expect(
          transformed?.contains('********'),
          true,
          reason: 'URL should be transformed to ******** for Android compatibility, got: $transformed',
        );
        
        // Should not contain production URLs
        expect(
          transformed?.contains('gameflex.io'),
          false,
          reason: 'Transformed URL should not contain production domain, got: $transformed',
        );
      }
    });

    test('Anonymous key should be available', () {
      final anonKey = SupabaseService.supabaseAnonKey;
      
      expect(anonKey.isNotEmpty, true, reason: 'Anonymous key should not be empty');
      expect(anonKey.startsWith('eyJ'), true, reason: 'Anonymous key should be a valid JWT token');
    });

    test('Null URL transformation should return null', () {
      expect(SupabaseService.transformUrl(null), null);
      expect(SupabaseService.transformUrl(''), '');
    });
  });
}
