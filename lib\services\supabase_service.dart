import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  // Environment configuration
  static const bool _isProduction = bool.fromEnvironment(
    'PRODUCTION',
    defaultValue: false,
  );

  // Supabase configuration - environment-aware URL selection
  static String get supabaseUrl {
    if (_isProduction) {
      // Production URLs - using Kong HTTP gateway port (8000)
      // Note: Using HTTP instead of HTTPS due to self-signed certificate issues
      return 'http://api.gameflex.io:8000';
    } else {
      // Development URLs - Smart URL selection for different network environments
      if (kIsWeb) {
        // For web, use localhost
        return 'http://localhost:8090';
      } else if (defaultTargetPlatform == TargetPlatform.android) {
        // For Android emulator, use ******** which maps to host machine's localhost
        return 'http://********:8090';
      } else {
        // For Windows, iOS, macOS, Linux - use localhost
        return 'http://localhost:8090';
      }
    }
  }

  // Environment-aware anonymous key configuration
  static String get supabaseAnonKey {
    if (_isProduction) {
      // Production anonymous key - replace with your production key
      // TODO: Replace with actual production anonymous key from your Supabase project
      return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';
    } else {
      // Development anonymous key (current local Supabase key)
      return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';
    }
  }

  /// Transform URLs based on environment and platform compatibility
  /// For development: ensures URLs work properly in Android emulator environment
  /// For production: uses production URLs
  /// ******** is the special IP that maps to the host machine's localhost
  static String? transformUrl(String? url) {
    if (url == null || url.isEmpty) return url;

    String transformedUrl = url;

    if (_isProduction) {
      // Production URL transformations - use port 8000 for Kong HTTP gateway
      transformedUrl = transformedUrl.replaceAll(
        'localhost:8090',
        'api.gameflex.io:8000',
      );
      transformedUrl = transformedUrl.replaceAll(
        '********:8090',
        'api.gameflex.io:8000',
      );
      transformedUrl = transformedUrl.replaceAll(
        'gameflex.local:8090',
        'api.gameflex.io:8000',
      );
      transformedUrl = transformedUrl.replaceAll(
        'api.gameflex.local:8090',
        'api.gameflex.io:8000',
      );
    } else {
      // Development URL transformations for Android emulator compatibility
      transformedUrl = transformedUrl.replaceAll(
        'localhost:8090',
        '********:8090',
      );
      transformedUrl = transformedUrl.replaceAll(
        'gameflex.local:8090',
        '********:8090',
      );
      transformedUrl = transformedUrl.replaceAll(
        'api.gameflex.local:8090',
        '********:8090',
      );
    }

    return transformedUrl;
  }

  SupabaseClient get client => Supabase.instance.client;

  // Static getter for client access
  static SupabaseClient get supabaseClient => Supabase.instance.client;

  static Future<void> initialize() async {
    try {
      final environment = _isProduction ? 'PRODUCTION' : 'DEVELOPMENT';
      developer.log(
        'SupabaseService: Initializing in $environment mode with URL: $supabaseUrl',
      );
      if (kDebugMode) {
        print('SupabaseService: Initializing Supabase in $environment mode...');
        print('SupabaseService: URL: $supabaseUrl');
      }

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: true, // Enable debug mode for development
      );

      developer.log('SupabaseService: Successfully initialized');
      if (kDebugMode) {
        print('SupabaseService: Successfully initialized');
      }
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: INITIALIZATION ERROR: $e');
        print('SupabaseService: INITIALIZATION STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  // Get current user
  User? get currentUser => client.auth.currentUser;

  // Get current session
  Session? get currentSession => client.auth.currentSession;

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Get auth stream for listening to auth state changes
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // Test database connection
  Future<bool> testConnection() async {
    try {
      developer.log('SupabaseService: Testing database connection...');
      if (kDebugMode) {
        print('SupabaseService: Testing database connection...');
      }

      // Try a simple query to test the connection
      final response = await client.from('posts').select('count').limit(1);

      developer.log(
        'SupabaseService: Connection test successful - Response: $response',
      );
      if (kDebugMode) {
        print(
          'SupabaseService: Connection test successful - Response: $response',
        );
      }
      return true;
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Connection test failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: CONNECTION TEST FAILED: $e');
        print('SupabaseService: CONNECTION TEST STACK TRACE: $stackTrace');
      }
      return false;
    }
  }
}
