import 'dart:developer' as developer;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/comment_model.dart';

class CommentsService {
  static final CommentsService _instance = CommentsService._internal();
  static CommentsService get instance => _instance;
  CommentsService._internal();

  final SupabaseClient _client = Supabase.instance.client;

  /// Fetch comments for a specific post
  Future<List<CommentModel>> getComments(String postId, {int limit = 50, int offset = 0}) async {
    try {
      developer.log(
        'CommentsService: Fetching comments for post $postId with limit=$limit, offset=$offset',
      );

      // Query comments with user information using a join
      final response = await _client
          .from('comments')
          .select('''
            id,
            post_id,
            user_id,
            content,
            like_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('post_id', postId)
          .eq('is_active', true)
          .order('created_at', ascending: true)
          .range(offset, offset + limit - 1);

      developer.log('CommentsService: Raw response: $response');

      final List<CommentModel> comments = [];
      for (final item in response) {
        try {
          // Flatten the user data
          final Map<String, dynamic> flattenedData = {
            ...item,
            'username': item['users']['username'],
            'display_name': item['users']['display_name'],
            'avatar_url': item['users']['avatar_url'],
          };
          flattenedData.remove('users');

          final comment = CommentModel.fromJson(flattenedData);
          comments.add(comment);
        } catch (e) {
          developer.log('CommentsService: Error parsing comment: $e');
          developer.log('CommentsService: Problematic item: $item');
        }
      }

      developer.log('CommentsService: Successfully fetched ${comments.length} comments');
      return comments;
    } catch (e) {
      developer.log('CommentsService: Error fetching comments: $e');
      rethrow;
    }
  }

  /// Create a new comment
  Future<CommentModel?> createComment({
    required String postId,
    required String content,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      developer.log('CommentsService: Creating comment for post $postId');

      final response = await _client
          .from('comments')
          .insert({
            'post_id': postId,
            'user_id': currentUser.id,
            'content': content,
          })
          .select('''
            id,
            post_id,
            user_id,
            content,
            like_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .single();

      // Flatten the user data
      final Map<String, dynamic> flattenedData = {
        ...response,
        'username': response['users']['username'],
        'display_name': response['users']['display_name'],
        'avatar_url': response['users']['avatar_url'],
      };
      flattenedData.remove('users');

      final comment = CommentModel.fromJson(flattenedData);

      // Update the post's comment count
      await _updatePostCommentCount(postId);

      developer.log('CommentsService: Successfully created comment');
      return comment;
    } catch (e) {
      developer.log('CommentsService: Error creating comment: $e');
      rethrow;
    }
  }

  /// Update an existing comment
  Future<CommentModel?> updateComment({
    required String commentId,
    required String content,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      developer.log('CommentsService: Updating comment $commentId');

      final response = await _client
          .from('comments')
          .update({
            'content': content,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', commentId)
          .eq('user_id', currentUser.id) // Ensure user can only update their own comments
          .select('''
            id,
            post_id,
            user_id,
            content,
            like_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .single();

      // Flatten the user data
      final Map<String, dynamic> flattenedData = {
        ...response,
        'username': response['users']['username'],
        'display_name': response['users']['display_name'],
        'avatar_url': response['users']['avatar_url'],
      };
      flattenedData.remove('users');

      final comment = CommentModel.fromJson(flattenedData);

      developer.log('CommentsService: Successfully updated comment');
      return comment;
    } catch (e) {
      developer.log('CommentsService: Error updating comment: $e');
      rethrow;
    }
  }

  /// Delete a comment (soft delete by setting is_active to false)
  Future<bool> deleteComment(String commentId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      developer.log('CommentsService: Deleting comment $commentId');

      // First get the comment to get the post_id for updating comment count
      final commentResponse = await _client
          .from('comments')
          .select('post_id')
          .eq('id', commentId)
          .eq('user_id', currentUser.id)
          .single();

      final postId = commentResponse['post_id'] as String;

      // Soft delete the comment
      await _client
          .from('comments')
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', commentId)
          .eq('user_id', currentUser.id); // Ensure user can only delete their own comments

      // Update the post's comment count
      await _updatePostCommentCount(postId);

      developer.log('CommentsService: Successfully deleted comment');
      return true;
    } catch (e) {
      developer.log('CommentsService: Error deleting comment: $e');
      return false;
    }
  }

  /// Update the comment count for a post
  Future<void> _updatePostCommentCount(String postId) async {
    try {
      // Get the current count of active comments for this post
      final countResponse = await _client
          .from('comments')
          .select('id')
          .eq('post_id', postId)
          .eq('is_active', true);

      final commentCount = countResponse.length;

      // Update the post's comment count
      await _client
          .from('posts')
          .update({'comment_count': commentCount})
          .eq('id', postId);

      developer.log('CommentsService: Updated post $postId comment count to $commentCount');
    } catch (e) {
      developer.log('CommentsService: Error updating post comment count: $e');
    }
  }
}
