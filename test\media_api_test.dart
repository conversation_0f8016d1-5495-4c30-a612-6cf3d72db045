import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  group('Media API Tests', () {
    const baseUrl = 'http://localhost:8090';
    const apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';
    
    test('Test media table structure and relationships', () async {
      print('🧪 Testing media table structure...');

      try {
        // 1. Fetch existing media records to verify structure
        final mediaResponse = await http.get(
          Uri.parse('$baseUrl/rest/v1/media?limit=3'),
          headers: {
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
          },
        );

        expect(mediaResponse.statusCode, 200);
        final mediaData = jsonDecode(mediaResponse.body) as List;
        expect(mediaData, isNotEmpty, reason: 'Media table should have seed data');
        
        final media = mediaData.first;
        expect(media['type'], isNotNull);
        expect(media['location'], isNotNull);
        expect(media['name'], isNotNull);
        expect(media['extension'], isNotNull);
        expect(media['owner_id'], isNotNull);
        expect(media['bucket_location'], isNotNull);
        expect(media['bucket_name'], isNotNull);
        expect(media['bucket_permission'], isNotNull);
        
        print('✅ Media table structure verified');
        print('📊 Sample media record: ${media['id']}');
        print('📊 Type: ${media['type']}, Location: ${media['location']}, Extension: ${media['extension']}');

        // 2. Fetch posts with media relationships
        final postsResponse = await http.get(
          Uri.parse('$baseUrl/rest/v1/posts?select=id,content,media_id,media(*)&limit=3'),
          headers: {
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
          },
        );

        expect(postsResponse.statusCode, 200);
        final postsData = jsonDecode(postsResponse.body) as List;
        expect(postsData, isNotEmpty, reason: 'Posts table should have seed data');
        
        final post = postsData.firstWhere((p) => p['media_id'] != null);
        expect(post['media_id'], isNotNull);
        expect(post['media'], isNotNull);
        expect(post['media']['id'], equals(post['media_id']));
        
        print('✅ Post-media relationship verified');
        print('📊 Post ID: ${post['id']}');
        print('📊 Media ID: ${post['media_id']}');
        print('📊 Media type: ${post['media']['type']}');

        // 3. Test URL construction
        final constructedUrl = _constructMediaUrl(post['media']);
        expect(constructedUrl, contains('http://localhost:8090'));
        expect(constructedUrl, contains('storage/v1/object/public/media'));
        
        print('🔗 Constructed URL: $constructedUrl');
        print('✅ URL construction verified');

        print('🎉 Media API tests completed successfully!');
      } catch (e, stackTrace) {
        print('❌ Media API test failed: $e');
        print('Stack trace: $stackTrace');
        rethrow;
      }
    });

    test('Test URL construction for different media types', () async {
      print('🧪 Testing URL construction scenarios...');

      // Test user media (no channel)
      final userMedia = {
        'location': 'user',
        'name': 'test_image_123',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-456',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final userUrl = _constructMediaUrl(userMedia);
      final expectedUserUrl = 'http://localhost:8090/storage/v1/object/public/media/user/user-456/test_image_123.jpg';
      expect(userUrl, equals(expectedUserUrl));
      print('✅ User media URL: $userUrl');

      // Test channel media
      final channelMedia = {
        'location': 'minecraft',
        'name': 'castle_build',
        'extension': 'png',
        'channel_id': 'channel-789',
        'owner_id': 'user-456',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final channelUrl = _constructMediaUrl(channelMedia);
      final expectedChannelUrl = 'http://localhost:8090/storage/v1/object/public/media/minecraft/user-456/channel-789/castle_build.png';
      expect(channelUrl, equals(expectedChannelUrl));
      print('✅ Channel media URL: $channelUrl');

      // Test video media
      final videoMedia = {
        'location': 'user',
        'name': 'gameplay_video',
        'extension': 'mp4',
        'channel_id': null,
        'owner_id': 'user-789',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final videoUrl = _constructMediaUrl(videoMedia);
      final expectedVideoUrl = 'http://localhost:8090/storage/v1/object/public/media/user/user-789/gameplay_video.mp4';
      expect(videoUrl, equals(expectedVideoUrl));
      print('✅ Video media URL: $videoUrl');

      // Test with custom base URL
      final customUrl = _constructMediaUrl(userMedia, baseUrl: 'https://api.gameflex.com');
      expect(customUrl, contains('https://api.gameflex.com'));
      expect(customUrl, contains('/storage/v1/object/public/media/user/user-456/test_image_123.jpg'));
      print('✅ Custom base URL: $customUrl');

      print('🎉 URL construction tests passed!');
    });

    test('Test media table query performance', () async {
      print('🧪 Testing media table query performance...');

      final stopwatch = Stopwatch()..start();

      // Test complex query with joins
      final response = await http.get(
        Uri.parse('$baseUrl/rest/v1/posts?select=id,content,created_at,media(type,location,name,extension,owner_id)&limit=10'),
        headers: {
          'apikey': apiKey,
          'Authorization': 'Bearer $apiKey',
        },
      );

      stopwatch.stop();

      expect(response.statusCode, 200);
      final data = jsonDecode(response.body) as List;
      expect(data, isNotEmpty);

      print('✅ Complex query completed in ${stopwatch.elapsedMilliseconds}ms');
      print('📊 Retrieved ${data.length} posts with media relationships');

      // Verify all posts with media have proper structure
      final postsWithMedia = data.where((post) => post['media'] != null).toList();
      for (final post in postsWithMedia) {
        expect(post['media']['type'], isNotNull);
        expect(post['media']['location'], isNotNull);
        expect(post['media']['name'], isNotNull);
        expect(post['media']['extension'], isNotNull);
      }

      print('✅ All media relationships properly structured');
      print('🎉 Performance test completed!');
    });
  });
}

/// Construct full URL from media record
String _constructMediaUrl(Map<String, dynamic> media, {String? baseUrl}) {
  final location = media['location'] as String;
  final name = media['name'] as String;
  final extension = media['extension'] as String;
  final channelId = media['channel_id'] as String?;
  final ownerId = media['owner_id'] as String;
  final bucketLocation = media['bucket_location'] as String? ?? 'storage/v1/object';
  final bucketName = media['bucket_name'] as String? ?? 'media';
  final bucketPermission = media['bucket_permission'] as String? ?? 'public';
  
  final host = baseUrl ?? 'http://localhost:8090';
  
  String path;
  if (channelId != null) {
    path = '$location/$ownerId/$channelId/$name.$extension';
  } else {
    path = '$location/$ownerId/$name.$extension';
  }
  
  return '$host/$bucketLocation/$bucketPermission/$bucketName/$path';
}
