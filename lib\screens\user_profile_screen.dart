import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/user_profile_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/post_card.dart';
import '../services/supabase_service.dart';

class UserProfileScreen extends StatefulWidget {
  final String userId;
  final String? username; // Optional, for display in app bar

  const UserProfileScreen({
    super.key,
    required this.userId,
    this.username,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Load user profile when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProfileProvider = Provider.of<UserProfileProvider>(
        context,
        listen: false,
      );
      userProfileProvider.loadUserProfile(widget.userId);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final userProfileProvider = Provider.of<UserProfileProvider>(
        context,
        listen: false,
      );
      if (userProfileProvider.hasMorePosts && !userProfileProvider.isLoading) {
        userProfileProvider.loadMorePosts();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<UserProfileProvider, AuthProvider>(
      builder: (context, userProfileProvider, authProvider, child) {
        final isOwnProfile = authProvider.user?.id == widget.userId;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.username ?? 'Profile',
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: [
              if (isOwnProfile)
                IconButton(
                  icon: const Icon(Icons.edit, color: AppColors.gfOffWhite),
                  onPressed: () {
                    // TODO: Navigate to edit profile
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Edit profile feature coming soon!'),
                        backgroundColor: AppColors.gfTeal,
                      ),
                    );
                  },
                ),
            ],
          ),
          body: _buildBody(userProfileProvider, isOwnProfile),
        );
      },
    );
  }

  Widget _buildBody(UserProfileProvider provider, bool isOwnProfile) {
    switch (provider.status) {
      case UserProfileStatus.initial:
      case UserProfileStatus.loading:
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
          ),
        );

      case UserProfileStatus.error:
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading profile',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.gfOffWhite,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  provider.error ?? 'Unknown error occurred',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => provider.loadUserProfile(widget.userId),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: Colors.black,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );

      case UserProfileStatus.loaded:
        return RefreshIndicator(
          onRefresh: () => provider.refreshProfile(),
          color: AppColors.gfGreen,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // User profile header
              SliverToBoxAdapter(
                child: _buildProfileHeader(provider, isOwnProfile),
              ),
              
              // Posts section
              SliverToBoxAdapter(
                child: _buildPostsHeader(provider),
              ),
              
              // Posts list
              _buildPostsList(provider),
              
              // Loading indicator for pagination
              if (provider.isLoading && provider.userPosts.isNotEmpty)
                const SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
    }
  }

  Widget _buildProfileHeader(UserProfileProvider provider, bool isOwnProfile) {
    final profile = provider.userProfile!;
    final stats = provider.userStats;

    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Profile Avatar
          _buildAvatar(profile),
          
          const SizedBox(height: 16),
          
          // Display Name
          Text(
            profile['display_name'] ?? 'User',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.gfOffWhite,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Username
          Text(
            '@${profile['username'] ?? 'user'}',
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.gfGrayText,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Bio (if available)
          if (profile['bio'] != null && profile['bio'].toString().isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                profile['bio'],
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.gfOffWhite,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          
          // Stats
          _buildStats(stats),
        ],
      ),
    );
  }

  Widget _buildAvatar(Map<String, dynamic> profile) {
    final avatarUrl = SupabaseService.transformUrl(profile['avatar_url']);
    final displayName = profile['display_name'] ?? 'User';

    if (avatarUrl != null && avatarUrl.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77),
        radius: 50,
        backgroundImage: NetworkImage(avatarUrl),
        onBackgroundImageError: (_, __) {},
        child: avatarUrl.isEmpty
            ? Text(
                displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
                style: const TextStyle(
                  color: AppColors.gfDarkBlue,
                  fontWeight: FontWeight.bold,
                  fontSize: 32,
                ),
              )
            : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77),
      radius: 50,
      child: Text(
        displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 32,
        ),
      ),
    );
  }

  Widget _buildStats(Map<String, int> stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.gfGreen.withValues(alpha: 77),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Posts', stats['posts']?.toString() ?? '0'),
          _buildStatItem('Followers', stats['followers']?.toString() ?? '0'),
          _buildStatItem('Following', stats['following']?.toString() ?? '0'),
          _buildStatItem('Likes', stats['likes']?.toString() ?? '0'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.gfGreen,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.gfGrayText,
          ),
        ),
      ],
    );
  }

  Widget _buildPostsHeader(UserProfileProvider provider) {
    final postCount = provider.userPosts.length;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Row(
        children: [
          const Text(
            'Posts',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '($postCount)',
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.gfGrayText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsList(UserProfileProvider provider) {
    if (provider.userPosts.isEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.all(48.0),
          child: Column(
            children: [
              const Icon(
                Icons.post_add_outlined,
                size: 64,
                color: AppColors.gfGrayText,
              ),
              const SizedBox(height: 16),
              const Text(
                'No posts yet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColors.gfOffWhite,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                widget.userId == Provider.of<AuthProvider>(context, listen: false).user?.id
                    ? 'Start sharing your gaming moments!'
                    : 'This user hasn\'t posted anything yet.',
                style: const TextStyle(
                  color: AppColors.gfGrayText,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final post = provider.userPosts[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: PostCard(
              post: post,
              onTap: () {
                // TODO: Navigate to post detail if needed
              },
            ),
          );
        },
        childCount: provider.userPosts.length,
      ),
    );
  }
}
