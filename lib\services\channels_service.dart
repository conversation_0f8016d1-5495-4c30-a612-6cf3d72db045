import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/channel_model.dart';
import '../models/post_model.dart';
import 'supabase_service.dart';

class ChannelsService {
  static ChannelsService? _instance;
  static ChannelsService get instance => _instance ??= ChannelsService._();

  ChannelsService._();

  SupabaseClient get _client => SupabaseService.instance.client;

  /// Fetch all public channels with user membership status
  Future<List<ChannelModel>> getChannels({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      developer.log(
        'ChannelsService: Fetching channels with limit=$limit, offset=$offset',
      );

      final currentUser = _client.auth.currentUser;
      final currentUserId = currentUser?.id ?? '';

      // Query channels with owner information and user membership status
      final response = await _client
          .from('channels')
          .select('''
            id,
            name,
            description,
            owner_id,
            is_public,
            is_active,
            member_count,
            created_at,
            updated_at,
            users(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('is_active', true)
          .eq('is_public', true)
          .order('member_count', ascending: false)
          .range(offset, offset + limit - 1);

      if (kDebugMode) {
        print('ChannelsService: Raw response: $response');
      }

      final List<ChannelModel> channels = [];

      for (final channelData in response) {
        // Get user membership status for this channel
        bool isUserMember = false;
        String? userRole;

        if (currentUserId.isNotEmpty) {
          try {
            final membershipResponse =
                await _client
                    .from('channel_members')
                    .select('role')
                    .eq('channel_id', channelData['id'])
                    .eq('user_id', currentUserId)
                    .eq('is_active', true)
                    .maybeSingle();

            if (membershipResponse != null) {
              isUserMember = true;
              userRole = membershipResponse['role'] as String?;
            }
          } catch (e) {
            developer.log(
              'ChannelsService: Error checking membership for channel ${channelData['id']}: $e',
            );
          }
        }

        // Parse owner information
        final ownerData = channelData['users'] as Map<String, dynamic>?;

        final channel = ChannelModel(
          id: channelData['id'] as String,
          name: channelData['name'] as String,
          description: channelData['description'] as String?,
          ownerId: channelData['owner_id'] as String,
          isPublic: channelData['is_public'] as bool? ?? true,
          isActive: channelData['is_active'] as bool? ?? true,
          memberCount: channelData['member_count'] as int? ?? 0,
          createdAt: DateTime.parse(channelData['created_at'] as String),
          updatedAt: DateTime.parse(channelData['updated_at'] as String),
          ownerUsername: ownerData?['username'] as String?,
          ownerDisplayName: ownerData?['display_name'] as String?,
          ownerAvatarUrl: SupabaseService.transformUrl(
            ownerData?['avatar_url'] as String?,
          ),
          isUserMember: isUserMember,
          userRole: userRole,
        );

        channels.add(channel);
      }

      developer.log(
        'ChannelsService: Successfully fetched ${channels.length} channels',
      );
      return channels;
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsService: Error fetching channels',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('ChannelsService ERROR: $e');
        print('ChannelsService STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  /// Get a specific channel by ID
  Future<ChannelModel?> getChannel(String channelId) async {
    try {
      final channels = await getChannels();
      return channels.where((channel) => channel.id == channelId).firstOrNull;
    } catch (e) {
      developer.log('ChannelsService: Error fetching channel $channelId: $e');
      return null;
    }
  }

  /// Get posts for a specific channel
  Future<List<PostModel>> getChannelPosts(
    String channelId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      developer.log(
        'ChannelsService: Fetching posts for channel $channelId with limit=$limit, offset=$offset',
      );

      final currentUser = _client.auth.currentUser;
      final currentUserId = currentUser?.id ?? '';

      // Use anon client for reading public posts to avoid JWT timing issues
      final anonClient = SupabaseClient(
        SupabaseService.supabaseUrl,
        SupabaseService.supabaseAnonKey,
      );

      final response = await anonClient
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('channel_id', channelId)
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final List<PostModel> posts = [];

      for (final postData in response) {
        // Check if current user liked this post
        bool isLikedByCurrentUser = false;
        if (currentUserId.isNotEmpty) {
          try {
            final likeResponse =
                await anonClient
                    .from('likes')
                    .select('id')
                    .eq('post_id', postData['id'])
                    .eq('user_id', currentUserId)
                    .maybeSingle();

            isLikedByCurrentUser = likeResponse != null;
          } catch (e) {
            developer.log('ChannelsService: Error checking like status: $e');
            // If we can't check like status, default to false
            isLikedByCurrentUser = false;
          }
        }

        final userData = postData['users'] as Map<String, dynamic>;

        final post = PostModel(
          id: postData['id'] as String,
          userId: postData['user_id'] as String,
          channelId: postData['channel_id'] as String?,
          content: postData['content'] as String? ?? '',
          mediaUrl: SupabaseService.transformUrl(
            postData['media_url'] as String?,
          ),
          mediaType: postData['media_type'] as String?,
          likeCount: postData['like_count'] as int? ?? 0,
          commentCount: postData['comment_count'] as int? ?? 0,
          isActive: postData['is_active'] as bool? ?? true,
          createdAt: DateTime.parse(postData['created_at'] as String),
          updatedAt: DateTime.parse(postData['updated_at'] as String),
          username: userData['username'] as String?,
          displayName: userData['display_name'] as String?,
          avatarUrl: SupabaseService.transformUrl(
            userData['avatar_url'] as String?,
          ),
          isLikedByCurrentUser: isLikedByCurrentUser,
        );

        posts.add(post);
      }

      developer.log(
        'ChannelsService: Successfully fetched ${posts.length} posts for channel $channelId',
      );
      return posts;
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsService: Error fetching posts for channel $channelId',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('ChannelsService ERROR: $e');
        print('ChannelsService STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  /// Join a channel
  Future<bool> joinChannel(String channelId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _client.from('channel_members').insert({
        'channel_id': channelId,
        'user_id': currentUser.id,
        'role': 'member',
      });

      // Update member count
      await _client.rpc(
        'increment_channel_members',
        params: {'channel_id': channelId},
      );

      developer.log('ChannelsService: Successfully joined channel $channelId');
      return true;
    } catch (e) {
      developer.log('ChannelsService: Error joining channel $channelId: $e');
      return false;
    }
  }

  /// Leave a channel
  Future<bool> leaveChannel(String channelId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('channel_members')
          .delete()
          .eq('channel_id', channelId)
          .eq('user_id', currentUser.id);

      // Update member count
      await _client.rpc(
        'decrement_channel_members',
        params: {'channel_id': channelId},
      );

      developer.log('ChannelsService: Successfully left channel $channelId');
      return true;
    } catch (e) {
      developer.log('ChannelsService: Error leaving channel $channelId: $e');
      return false;
    }
  }
}
