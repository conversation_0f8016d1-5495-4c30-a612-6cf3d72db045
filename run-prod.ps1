#!/usr/bin/env pwsh
# Production Run Script for GameFlex Mobile
# This script runs the app in production mode with gameflex.io URLs

param(
    [string]$Platform = "windows",
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Production Run Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\run-prod.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\run-prod.ps1                    # Run Windows production"
    Write-Host "  .\run-prod.ps1 -Platform android  # Run Android production"
    Write-Host "  .\run-prod.ps1 -Platform ios      # Run iOS production"
    Write-Host "  .\run-prod.ps1 -Platform web      # Run Web production"
    Write-Host ""
    Write-Host "⚠️  Warning: This connects to production servers!" -ForegroundColor Red
    exit 0
}

Write-Host "🚀 Running GameFlex Mobile in PRODUCTION mode" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: Release (Production)" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

Write-Host "🔧 Production Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses production backend (https://api.gameflex.io)" -ForegroundColor White
Write-Host "  - Release mode optimizations" -ForegroundColor White
Write-Host "  - Production app name/icon" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Warning: This will connect to production servers!" -ForegroundColor Red
Write-Host ""

# Confirm production run
$confirmation = Read-Host "Are you sure you want to run in PRODUCTION mode? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "❌ Production run cancelled" -ForegroundColor Yellow
    exit 0
}

switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏃 Running on Windows (production)..." -ForegroundColor Yellow
        flutter run -d windows --release --dart-define=PRODUCTION=true
    }
    "android" {
        Write-Host "🏃 Running on Android (production)..." -ForegroundColor Yellow
        flutter run -d android --release --flavor production --dart-define=PRODUCTION=true
    }
    "ios" {
        Write-Host "🏃 Running on iOS (production)..." -ForegroundColor Yellow
        flutter run -d ios --release --dart-define=PRODUCTION=true
    }
    "web" {
        Write-Host "🏃 Running on Web (production)..." -ForegroundColor Yellow
        flutter run -d web-server --release --dart-define=PRODUCTION=true --web-port 3001
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}
