import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/upload_service.dart';

void main() {
  group('Staging Media URL Tests', () {
    test('MediaUrlHelper should use staging URL when STAGING=true', () {
      // This test should be run with: flutter test --dart-define=STAGING=true
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      if (!isStaging) {
        // Skip this test if not running in staging mode
        return;
      }

      // Test media data
      final testMedia = {
        'type': 'image',
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      // Test URL construction without explicit baseUrl (should use environment-aware URL)
      final constructedUrl = MediaUrlHelper.constructMediaUrl(testMedia);

      // Should use staging URL
      expect(
        constructedUrl.contains('dev.api.gameflex.io:8000'),
        true,
        reason:
            'Staging should use dev.api.gameflex.io:8000, got: $constructedUrl',
      );

      // Should not use local URLs
      expect(
        constructedUrl.contains('localhost') ||
            constructedUrl.contains('********'),
        false,
        reason: 'Staging should not use local URLs, got: $constructedUrl',
      );

      // Should have correct path structure
      expect(
        constructedUrl,
        equals(
          'http://dev.api.gameflex.io:8000/storage/v1/object/public/media/user/user-123/test_image.jpg',
        ),
        reason: 'URL should match expected staging format',
      );
    });

    test('MediaUrlHelper should respect explicit baseUrl parameter', () {
      final testMedia = {
        'type': 'image',
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      // Test with explicit staging baseUrl
      final explicitUrl = MediaUrlHelper.constructMediaUrl(
        testMedia,
        baseUrl: 'http://dev.api.gameflex.io:8000',
      );

      expect(
        explicitUrl,
        equals(
          'http://dev.api.gameflex.io:8000/storage/v1/object/public/media/user/user-123/test_image.jpg',
        ),
        reason: 'Explicit baseUrl should be used correctly',
      );
    });
  });
}
