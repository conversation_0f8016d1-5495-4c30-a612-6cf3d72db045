import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import '../models/image_resolution.dart';
import '../models/clipart_model.dart';

class ImageUtils {
  /// Load image from file and get its dimensions
  static Future<Size> getImageDimensions(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);
    if (image == null) {
      throw Exception('Failed to decode image');
    }
    return Size(image.width.toDouble(), image.height.toDouble());
  }

  /// Crop and resize image based on the selected area and target resolution
  static Future<File> processImage({
    required File originalFile,
    required ImageResolution targetResolution,
    required Rect cropArea,
    required Size originalImageSize,
  }) async {
    return processImageWithBackground(
      originalFile: originalFile,
      targetResolution: targetResolution,
      cropArea: cropArea,
      originalImageSize: originalImageSize,
      backgroundColor: Colors.white,
    );
  }

  /// Add clip art overlays to an already processed image
  static Future<File> processImageWithClipArt({
    required File originalFile,
    required List<ClipArtOverlay> clipArtOverlays,
  }) async {
    try {
      // If no clip art overlays, return the original file
      if (clipArtOverlays.isEmpty) {
        return originalFile;
      }

      // Read the original image
      final bytes = await originalFile.readAsBytes();
      final baseImg = img.decodeImage(bytes);
      if (baseImg == null) {
        throw Exception('Failed to decode base image');
      }

      // Use the fixed resolution that matches our app's standard (9:16)
      // The cropped image should already be in this format
      final targetResolution = ImageResolution.fixedResolution;

      // Composite each clip art overlay
      for (final overlay in clipArtOverlays) {
        await _compositeClipArt(baseImg, overlay, targetResolution);
      }

      // Save the final composited image
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final finalFile = File('${tempDir.path}/final_image_$timestamp.jpg');

      final jpegBytes = img.encodeJpg(baseImg, quality: 90);
      await finalFile.writeAsBytes(jpegBytes);

      return finalFile;
    } catch (e) {
      print('Error processing image with clip art: $e');
      rethrow;
    }
  }

  /// Crop and resize image with custom background color for blank space and clip art overlays
  static Future<File> processImageWithClipArtAndCrop({
    required File originalFile,
    required ImageResolution targetResolution,
    required Rect cropArea,
    required Size originalImageSize,
    required Color backgroundColor,
    required List<ClipArtOverlay> clipArtOverlays,
  }) async {
    try {
      // First process the base image
      final baseImage = await processImageWithBackground(
        originalFile: originalFile,
        targetResolution: targetResolution,
        cropArea: cropArea,
        originalImageSize: originalImageSize,
        backgroundColor: backgroundColor,
      );

      // If no clip art overlays, return the base image
      if (clipArtOverlays.isEmpty) {
        return baseImage;
      }

      // Read the base image
      final baseBytes = await baseImage.readAsBytes();
      final baseImg = img.decodeImage(baseBytes);
      if (baseImg == null) {
        throw Exception('Failed to decode base image');
      }

      // Composite each clip art overlay
      for (final overlay in clipArtOverlays) {
        await _compositeClipArt(baseImg, overlay, targetResolution);
      }

      // Save the final composited image
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final finalFile = File('${tempDir.path}/final_image_$timestamp.jpg');

      final jpegBytes = img.encodeJpg(baseImg, quality: 90);
      await finalFile.writeAsBytes(jpegBytes);

      // Clean up the temporary base image
      await baseImage.delete();

      return finalFile;
    } catch (e) {
      print('Error processing image with clip art: $e');
      rethrow;
    }
  }

  /// Crop and resize image with custom background color for blank space
  static Future<File> processImageWithBackground({
    required File originalFile,
    required ImageResolution targetResolution,
    required Rect cropArea,
    required Size originalImageSize,
    required Color backgroundColor,
  }) async {
    try {
      // Read the original image
      final bytes = await originalFile.readAsBytes();
      final originalImage = img.decodeImage(bytes);
      if (originalImage == null) {
        throw Exception('Failed to decode image');
      }

      // Create a new image with the target resolution and background color
      final resultImage = img.Image(
        width: targetResolution.width,
        height: targetResolution.height,
      );

      // Fill with background color
      final bgColor = img.ColorRgb8(
        (backgroundColor.r * 255).round(),
        (backgroundColor.g * 255).round(),
        (backgroundColor.b * 255).round(),
      );
      img.fill(resultImage, color: bgColor);

      // Calculate crop coordinates in image pixels
      final cropX = (cropArea.left * originalImage.width).round();
      final cropY = (cropArea.top * originalImage.height).round();
      final cropWidth = (cropArea.width * originalImage.width).round();
      final cropHeight = (cropArea.height * originalImage.height).round();

      // Crop the image
      final croppedImage = img.copyCrop(
        originalImage,
        x: cropX.clamp(0, originalImage.width),
        y: cropY.clamp(0, originalImage.height),
        width: cropWidth.clamp(
          1,
          originalImage.width - cropX.clamp(0, originalImage.width),
        ),
        height: cropHeight.clamp(
          1,
          originalImage.height - cropY.clamp(0, originalImage.height),
        ),
      );

      // Calculate how to fit the cropped image into the target resolution while maintaining aspect ratio
      final croppedAspectRatio = croppedImage.width / croppedImage.height;
      final targetAspectRatio =
          targetResolution.width / targetResolution.height;

      int finalWidth, finalHeight;
      int offsetX = 0, offsetY = 0;

      if (croppedAspectRatio > targetAspectRatio) {
        // Cropped image is wider - fit to width
        finalWidth = targetResolution.width;
        finalHeight = (targetResolution.width / croppedAspectRatio).round();
        offsetY = ((targetResolution.height - finalHeight) / 2).round();
      } else {
        // Cropped image is taller - fit to height
        finalHeight = targetResolution.height;
        finalWidth = (targetResolution.height * croppedAspectRatio).round();
        offsetX = ((targetResolution.width - finalWidth) / 2).round();
      }

      // Resize the cropped image to the calculated dimensions
      final resizedImage = img.copyResize(
        croppedImage,
        width: finalWidth,
        height: finalHeight,
        interpolation: img.Interpolation.cubic,
      );

      // Composite the resized image onto the background at the calculated offset
      img.compositeImage(
        resultImage,
        resizedImage,
        dstX: offsetX,
        dstY: offsetY,
      );

      // Save to temporary file
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempFile = File('${tempDir.path}/processed_image_$timestamp.jpg');

      final jpegBytes = img.encodeJpg(resultImage, quality: 90);
      await tempFile.writeAsBytes(jpegBytes);

      return tempFile;
    } catch (e) {
      print('Error processing image: $e');
      rethrow;
    }
  }

  /// Calculate the scale and offset to fit an image within a container
  static Map<String, double> calculateImageFit({
    required Size imageSize,
    required Size containerSize,
    required BoxFit fit,
  }) {
    final imageAspectRatio = imageSize.width / imageSize.height;
    final containerAspectRatio = containerSize.width / containerSize.height;

    double scale;
    double offsetX = 0;
    double offsetY = 0;

    switch (fit) {
      case BoxFit.contain:
        if (imageAspectRatio > containerAspectRatio) {
          // Image is wider than container
          scale = containerSize.width / imageSize.width;
          offsetY = (containerSize.height - (imageSize.height * scale)) / 2;
        } else {
          // Image is taller than container
          scale = containerSize.height / imageSize.height;
          offsetX = (containerSize.width - (imageSize.width * scale)) / 2;
        }
        break;
      case BoxFit.cover:
        if (imageAspectRatio > containerAspectRatio) {
          // Image is wider than container
          scale = containerSize.height / imageSize.height;
          offsetX = (containerSize.width - (imageSize.width * scale)) / 2;
        } else {
          // Image is taller than container
          scale = containerSize.width / imageSize.width;
          offsetY = (containerSize.height - (imageSize.height * scale)) / 2;
        }
        break;
      case BoxFit.fill:
        scale = 1.0; // Will be handled by the widget
        break;
      default:
        scale = 1.0;
    }

    return {'scale': scale, 'offsetX': offsetX, 'offsetY': offsetY};
  }

  /// Convert a Rect from widget coordinates to image coordinates (0.0 to 1.0)
  static Rect normalizeRect(Rect rect, Size containerSize) {
    return Rect.fromLTWH(
      rect.left / containerSize.width,
      rect.top / containerSize.height,
      rect.width / containerSize.width,
      rect.height / containerSize.height,
    );
  }

  /// Validate if the image file is supported
  static bool isValidImageFile(File file) {
    final extension = file.path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Get file size in MB
  static Future<double> getFileSizeMB(File file) async {
    final bytes = await file.length();
    return bytes / (1024 * 1024);
  }

  /// Composite a clip art overlay onto the base image
  static Future<void> _compositeClipArt(
    img.Image baseImage,
    ClipArtOverlay overlay,
    ImageResolution targetResolution,
  ) async {
    try {
      // Load the clip art asset
      final assetBytes = await rootBundle.load(overlay.item.assetPath);
      final clipArtBytes = assetBytes.buffer.asUint8List();
      final clipArtImage = img.decodePng(clipArtBytes);

      if (clipArtImage == null) {
        print('Failed to decode clip art: ${overlay.item.assetPath}');
        return;
      }

      // Calculate the size and position of the clip art on the final image
      // Use a proportional size based on image dimensions (18% of smaller dimension)
      final baseSize =
          (targetResolution.width < targetResolution.height
              ? targetResolution.width
              : targetResolution.height) *
          0.18;
      final scaledSize = (baseSize * overlay.scale).round();

      // Resize the clip art
      final resizedClipArt = img.copyResize(
        clipArtImage,
        width: scaledSize,
        height: scaledSize,
        interpolation: img.Interpolation.cubic,
      );

      // Apply rotation if needed
      img.Image rotatedClipArt = resizedClipArt;
      if (overlay.rotation != 0.0) {
        final rotationDegrees = (overlay.rotation * 180 / 3.14159).round();
        rotatedClipArt = img.copyRotate(resizedClipArt, angle: rotationDegrees);
      }

      // Calculate position on the final image
      final x =
          (overlay.x * targetResolution.width - rotatedClipArt.width / 2)
              .round();
      final y =
          (overlay.y * targetResolution.height - rotatedClipArt.height / 2)
              .round();

      // Composite the clip art onto the base image
      img.compositeImage(
        baseImage,
        rotatedClipArt,
        dstX: x.clamp(0, targetResolution.width - rotatedClipArt.width),
        dstY: y.clamp(0, targetResolution.height - rotatedClipArt.height),
        blend: img.BlendMode.alpha,
      );
    } catch (e) {
      print('Error compositing clip art ${overlay.item.assetPath}: $e');
      // Continue with other overlays even if one fails
    }
  }
}
