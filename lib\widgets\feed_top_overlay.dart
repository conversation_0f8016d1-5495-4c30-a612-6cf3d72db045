import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../screens/user_profile_screen.dart';

class FeedTopOverlay extends StatelessWidget {
  final PostModel post;

  const FeedTopOverlay({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 16,
      right: 80,
      top: 40, // Moved higher up with less margin
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info
          GestureDetector(
            onTap: () => _navigateToUserProfile(context),
            child: Row(
              children: [
                _buildAvatar(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorDisplayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontSize: 16,
                          shadows: [
                            Shadow(
                              color: Colors.black,
                              blurRadius: 2,
                              offset: Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '@${post.authorUsername}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          shadows: [
                            Shadow(
                              color: Colors.black,
                              blurRadius: 2,
                              offset: Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Post content (smaller text)
          if (post.content.isNotEmpty)
            Text(
              post.content,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13, // Reduced from 16 to 13
                height: 1.3,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
              maxLines: 2, // Reduced from 3 to 2 lines
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => UserProfileScreen(
              userId: post.userId,
              username: post.authorUsername,
            ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (post.avatarUrl != null && post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            post.avatarUrl!.isEmpty
                ? Text(
                  post.authorDisplayName.isNotEmpty
                      ? post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        post.authorDisplayName.isNotEmpty
            ? post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }
}
