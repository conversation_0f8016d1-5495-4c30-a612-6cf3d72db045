import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/like_model.dart';
import '../services/posts_service.dart';
import '../widgets/common/gf_button.dart';

class LikedPostsScreen extends StatefulWidget {
  const LikedPostsScreen({super.key});

  @override
  State<LikedPostsScreen> createState() => _LikedPostsScreenState();
}

class _LikedPostsScreenState extends State<LikedPostsScreen> {
  List<LikeModel> _likedPosts = [];
  bool _isLoading = true;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _loadLikedPosts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreLikedPosts();
    }
  }

  Future<void> _loadLikedPosts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final likedPosts = await PostsService.instance.getCurrentUserLikedPosts(
        limit: _pageSize,
        offset: 0,
      );

      setState(() {
        _likedPosts = likedPosts;
        _isLoading = false;
        _hasMore = likedPosts.length == _pageSize;
        _currentOffset = likedPosts.length;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreLikedPosts() async {
    if (!_hasMore || _isLoading) return;

    try {
      final moreLikedPosts = await PostsService.instance
          .getCurrentUserLikedPosts(limit: _pageSize, offset: _currentOffset);

      setState(() {
        _likedPosts.addAll(moreLikedPosts);
        _hasMore = moreLikedPosts.length == _pageSize;
        _currentOffset += moreLikedPosts.length;
      });
    } catch (e) {
      // Handle error silently for pagination
    }
  }

  Future<void> _refreshLikedPosts() async {
    _currentOffset = 0;
    await _loadLikedPosts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Liked Posts',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _likedPosts.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_errorMessage != null && _likedPosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load liked posts',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            GFButton(
              text: 'Retry',
              onPressed: _loadLikedPosts,
              type: GFButtonType.primary,
            ),
          ],
        ),
      );
    }

    if (_likedPosts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.shield_outlined, size: 64, color: AppColors.gfGrayText),
            SizedBox(height: 16),
            Text(
              'No liked posts yet',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Posts you like will appear here',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshLikedPosts,
      color: AppColors.gfGreen,
      backgroundColor: AppColors.gfDarkBackground,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _likedPosts.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _likedPosts.length) {
            // Loading indicator for pagination
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                ),
              ),
            );
          }

          final like = _likedPosts[index];
          return _buildLikeCard(like);
        },
      ),
    );
  }

  Widget _buildLikeCard(LikeModel like) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppColors.gfCardBackground,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user info and time
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.gfGreen,
                  backgroundImage:
                      like.avatarUrl != null
                          ? NetworkImage(like.avatarUrl!)
                          : null,
                  child:
                      like.avatarUrl == null
                          ? Text(
                            (like.displayName ?? like.username ?? 'U')[0]
                                .toUpperCase(),
                            style: const TextStyle(
                              color: AppColors.gfDarkBackground,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        like.displayName ?? like.username ?? 'Unknown User',
                        style: const TextStyle(
                          color: AppColors.gfOffWhite,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        'Liked ${like.timeAgo}',
                        style: const TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.shield, color: AppColors.gfGreen, size: 20),
              ],
            ),
            const SizedBox(height: 12),
            // Post content
            if (like.postContent != null && like.postContent!.isNotEmpty)
              Text(
                like.postContent!,
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 14,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            // Post media
            if (like.postMediaUrl != null && like.postMediaUrl!.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 8),
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: NetworkImage(like.postMediaUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
