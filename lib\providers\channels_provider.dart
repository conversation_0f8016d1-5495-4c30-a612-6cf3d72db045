import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/channel_model.dart';
import '../models/post_model.dart';
import '../services/channels_service.dart';
import '../services/supabase_service.dart';

enum ChannelsStatus { initial, loading, loaded, error, refreshing }

class ChannelsProvider extends ChangeNotifier {
  ChannelsStatus _status = ChannelsStatus.initial;
  List<ChannelModel> _channels = [];
  String? _errorMessage;
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _pageSize = 50;

  // Selected channel and its posts
  ChannelModel? _selectedChannel;
  List<PostModel> _channelPosts = [];
  bool _channelPostsLoading = false;
  String? _channelPostsError;

  // Real-time subscriptions
  RealtimeChannel? _channelsSubscription;

  ChannelsStatus get status => _status;
  List<ChannelModel> get channels => _channels;
  String? get errorMessage => _errorMessage;
  bool get hasMore => _hasMore;
  bool get isLoading => _status == ChannelsStatus.loading;
  bool get isRefreshing => _status == ChannelsStatus.refreshing;

  ChannelModel? get selectedChannel => _selectedChannel;
  List<PostModel> get channelPosts => _channelPosts;
  bool get channelPostsLoading => _channelPostsLoading;
  String? get channelPostsError => _channelPostsError;

  /// Load initial channels
  Future<void> loadChannels() async {
    if (_status == ChannelsStatus.loading) return;

    developer.log('ChannelsProvider: Starting to load channels');
    _setStatus(ChannelsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;

    try {
      // Test connection first
      developer.log('ChannelsProvider: Testing Supabase connection...');
      final connectionOk = await SupabaseService.instance.testConnection();
      if (!connectionOk) {
        throw Exception('Database connection failed');
      }

      developer.log('ChannelsProvider: Calling ChannelsService.getChannels()');
      final channels = await ChannelsService.instance.getChannels(
        limit: _pageSize,
        offset: _currentOffset,
      );

      developer.log('ChannelsProvider: Received ${channels.length} channels');
      _channels = channels;
      _currentOffset = channels.length;
      _hasMore = channels.length == _pageSize;
      _setStatus(ChannelsStatus.loaded);
      developer.log('ChannelsProvider: Successfully loaded channels');
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error loading channels',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('ChannelsProvider ERROR: $e');
        print('ChannelsProvider STACK TRACE: $stackTrace');
      }
      _setError('Failed to load channels: $e');
    }
  }

  /// Refresh channels (pull to refresh)
  Future<void> refreshChannels() async {
    if (_status == ChannelsStatus.refreshing) return;

    developer.log('ChannelsProvider: Starting to refresh channels');
    _setStatus(ChannelsStatus.refreshing);
    _currentOffset = 0;
    _hasMore = true;

    try {
      final channels = await ChannelsService.instance.getChannels(
        limit: _pageSize,
        offset: 0,
      );

      developer.log(
        'ChannelsProvider: Refreshed with ${channels.length} channels',
      );
      _channels = channels;
      _currentOffset = channels.length;
      _hasMore = channels.length == _pageSize;
      _setStatus(ChannelsStatus.loaded);
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error refreshing channels',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('ChannelsProvider REFRESH ERROR: $e');
        print('ChannelsProvider REFRESH STACK TRACE: $stackTrace');
      }
      _setError('Failed to refresh channels: $e');
    }
  }

  /// Load more channels (pagination)
  Future<void> loadMoreChannels() async {
    if (!_hasMore || _status == ChannelsStatus.loading) return;

    try {
      final moreChannels = await ChannelsService.instance.getChannels(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (moreChannels.isNotEmpty) {
        _channels.addAll(moreChannels);
        _currentOffset += moreChannels.length;
        _hasMore = moreChannels.length == _pageSize;
        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more channels: $e');
    }
  }

  /// Select a channel and load its posts
  Future<void> selectChannel(ChannelModel channel) async {
    _selectedChannel = channel;
    _channelPosts = [];
    _channelPostsLoading = true;
    _channelPostsError = null;
    notifyListeners();

    try {
      developer.log(
        'ChannelsProvider: Loading posts for channel ${channel.name}',
      );
      final posts = await ChannelsService.instance.getChannelPosts(channel.id);

      _channelPosts = posts;
      _channelPostsLoading = false;
      developer.log(
        'ChannelsProvider: Loaded ${posts.length} posts for channel ${channel.name}',
      );
      notifyListeners();
    } catch (e, stackTrace) {
      developer.log(
        'ChannelsProvider: Error loading posts for channel ${channel.name}',
        error: e,
        stackTrace: stackTrace,
      );
      _channelPostsError = 'Failed to load channel posts: $e';
      _channelPostsLoading = false;
      notifyListeners();
    }
  }

  /// Clear selected channel
  void clearSelectedChannel() {
    _selectedChannel = null;
    _channelPosts = [];
    _channelPostsLoading = false;
    _channelPostsError = null;
    notifyListeners();
  }

  /// Join a channel
  Future<bool> joinChannel(String channelId) async {
    try {
      final success = await ChannelsService.instance.joinChannel(channelId);
      if (success) {
        // Update the channel in the list
        final channelIndex = _channels.indexWhere(
          (channel) => channel.id == channelId,
        );
        if (channelIndex != -1) {
          _channels[channelIndex] = _channels[channelIndex].copyWith(
            isUserMember: true,
            userRole: 'member',
            memberCount: _channels[channelIndex].memberCount + 1,
          );
          notifyListeners();
        }
      }
      return success;
    } catch (e) {
      developer.log('ChannelsProvider: Error joining channel: $e');
      return false;
    }
  }

  /// Leave a channel
  Future<bool> leaveChannel(String channelId) async {
    try {
      final success = await ChannelsService.instance.leaveChannel(channelId);
      if (success) {
        // Update the channel in the list
        final channelIndex = _channels.indexWhere(
          (channel) => channel.id == channelId,
        );
        if (channelIndex != -1) {
          _channels[channelIndex] = _channels[channelIndex].copyWith(
            isUserMember: false,
            userRole: null,
            memberCount: _channels[channelIndex].memberCount - 1,
          );
          notifyListeners();
        }
      }
      return success;
    } catch (e) {
      developer.log('ChannelsProvider: Error leaving channel: $e');
      return false;
    }
  }

  void _setStatus(ChannelsStatus status) {
    _status = status;
    if (status != ChannelsStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _status = ChannelsStatus.error;
    _errorMessage = message;
    notifyListeners();
  }

  /// Start real-time subscriptions
  void startRealtimeSubscriptions() {
    try {
      final client = SupabaseService.instance.client;

      // Subscribe to channels table changes
      _channelsSubscription =
          client
              .channel('channels_changes')
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'channels',
                callback: _handleChannelsChange,
              )
              .subscribe();

      developer.log('ChannelsProvider: Real-time subscriptions started');
    } catch (e) {
      developer.log(
        'ChannelsProvider: Failed to start real-time subscriptions: $e',
      );
    }
  }

  /// Stop real-time subscriptions
  void stopRealtimeSubscriptions() {
    _channelsSubscription?.unsubscribe();
    _channelsSubscription = null;
    developer.log('ChannelsProvider: Real-time subscriptions stopped');
  }

  /// Handle channels table changes
  void _handleChannelsChange(PostgresChangePayload payload) {
    developer.log(
      'ChannelsProvider: Channels change detected: ${payload.eventType}',
    );

    switch (payload.eventType) {
      case PostgresChangeEvent.insert:
        _handleChannelInsert(payload.newRecord);
        break;
      case PostgresChangeEvent.update:
        _handleChannelUpdate(payload.newRecord);
        break;
      case PostgresChangeEvent.delete:
        _handleChannelDelete(payload.oldRecord);
        break;
      case PostgresChangeEvent.all:
        // Handle all events - this shouldn't happen in practice
        break;
    }
  }

  /// Handle channel insertion
  void _handleChannelInsert(Map<String, dynamic> record) {
    // For simplicity, just refresh the channels list
    // In a production app, you might want to insert the new channel directly
    refreshChannels();
  }

  /// Handle channel update
  void _handleChannelUpdate(Map<String, dynamic> record) {
    final channelId = record['id'] as String?;
    if (channelId == null) return;

    final channelIndex = _channels.indexWhere(
      (channel) => channel.id == channelId,
    );
    if (channelIndex != -1) {
      // Update the channel with new data
      final updatedChannel = _channels[channelIndex].copyWith(
        name: record['name'] as String?,
        description: record['description'] as String?,
        memberCount: record['member_count'] as int?,
        isActive: record['is_active'] as bool?,
      );
      _channels[channelIndex] = updatedChannel;
      notifyListeners();
    }
  }

  /// Handle channel deletion
  void _handleChannelDelete(Map<String, dynamic> record) {
    final channelId = record['id'] as String?;
    if (channelId == null) return;

    _channels.removeWhere((channel) => channel.id == channelId);

    // Clear selected channel if it was deleted
    if (_selectedChannel?.id == channelId) {
      clearSelectedChannel();
    }

    notifyListeners();
  }

  @override
  void dispose() {
    stopRealtimeSubscriptions();
    super.dispose();
  }
}
