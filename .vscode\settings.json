{"dart.flutterSdkPath": "P:\\flutter_sdk\\flutter", "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterHotReloadOnSave": "always", "dart.flutterHotRestartOnSave": "never", "dart.openDevTools": "flutter", "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.evaluateGettersInDebugViews": true, "dart.evaluateToStringInDebugViews": true, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "windows"], "dart.flutterSelectDeviceWhenConnected": true, "dart.flutterTrackWidgetCreation": true, "dart.showInspectorNotificationsForWidgetErrors": true, "dart.warnWhenEditingFilesOutsideWorkspace": true, "dart.warnWhenEditingFilesInPubCache": true, "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "flutter.defaultLaunchDevice": "emulator-5554"}