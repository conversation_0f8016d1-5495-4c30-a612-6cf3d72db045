#include "Generated.xcconfig"

// Production configuration for GameFlex Mobile
PRODUCT_BUNDLE_IDENTIFIER = com.example.gameflexMobile
BUNDLE_DISPLAY_NAME = GameFlex
BUNDLE_NAME = GameFlex

// Production-specific settings
FLUTTER_BUILD_MODE = release
DART_DEFINES = PRODUCTION=true

// Code signing (adjust as needed for your distribution team)
CODE_SIGN_IDENTITY = iPhone Distribution
DEVELOPMENT_TEAM = 

// Deployment target
IPHONEOS_DEPLOYMENT_TARGET = 12.0

// Production optimizations
ENABLE_BITCODE = NO
VALIDATE_PRODUCT = YES
