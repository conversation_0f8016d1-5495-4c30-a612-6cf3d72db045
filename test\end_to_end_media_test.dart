import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  group('End-to-End Media Tests', () {
    const baseUrl = 'http://localhost:8090';
    const apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';
    
    test('Complete media workflow: create media, create post, fetch with relationships', () async {
      print('🧪 Testing complete media workflow...');

      try {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        
        // 1. Create a media record
        print('📸 Creating media record...');
        final mediaResponse = await http.post(
          Uri.parse('$baseUrl/rest/v1/media'),
          headers: {
            'Content-Type': 'application/json',
            'apikey': api<PERSON><PERSON>,
            'Authorization': 'Bearer $apiKey',
            'Prefer': 'return=representation',
          },
          body: jsonEncode({
            'type': 'image',
            'location': 'user',
            'name': 'test_workflow_$timestamp',
            'extension': 'jpg',
            'channel_id': null,
            'owner_id': '00000000-0000-0000-0000-000000000001',
            'bucket_location': 'storage/v1/object',
            'bucket_name': 'media',
            'bucket_permission': 'public',
          }),
        );

        expect(mediaResponse.statusCode, 201);
        final mediaData = jsonDecode(mediaResponse.body)[0];
        final mediaId = mediaData['id'];
        print('✅ Media record created: $mediaId');

        // 2. Create a post that references the media
        print('📝 Creating post with media reference...');
        final postResponse = await http.post(
          Uri.parse('$baseUrl/rest/v1/posts'),
          headers: {
            'Content-Type': 'application/json',
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
            'Prefer': 'return=representation',
          },
          body: jsonEncode({
            'user_id': '00000000-0000-0000-0000-000000000001',
            'media_id': mediaId,
            'content': 'End-to-end test post with media workflow - $timestamp',
            'channel_id': null,
          }),
        );

        expect(postResponse.statusCode, 201);
        final postData = jsonDecode(postResponse.body)[0];
        final postId = postData['id'];
        print('✅ Post created: $postId');

        // 3. Fetch posts with media relationships (simulating PostsService query)
        print('🔍 Fetching posts with media relationships...');
        final fetchResponse = await http.get(
          Uri.parse('$baseUrl/rest/v1/posts?select=id,user_id,channel_id,content,media_id,like_count,comment_count,is_active,created_at,updated_at,media(type,location,name,extension,channel_id,owner_id,bucket_location,bucket_name,bucket_permission),users!inner(username,display_name,avatar_url)&id=eq.$postId'),
          headers: {
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
          },
        );

        expect(fetchResponse.statusCode, 200);
        final fetchedPosts = jsonDecode(fetchResponse.body);
        expect(fetchedPosts, isNotEmpty);
        
        final post = fetchedPosts[0];
        expect(post['media_id'], equals(mediaId));
        expect(post['media'], isNotNull);
        expect(post['media']['id'], equals(mediaId));
        expect(post['media']['type'], equals('image'));
        expect(post['media']['location'], equals('user'));
        expect(post['media']['name'], equals('test_workflow_$timestamp'));
        expect(post['media']['extension'], equals('jpg'));
        
        print('✅ Post-media relationship verified');

        // 4. Test URL construction from fetched data
        print('🔗 Testing URL construction...');
        final media = post['media'];
        final constructedUrl = _constructMediaUrl(media);
        final expectedUrl = 'http://localhost:8090/storage/v1/object/public/media/user/00000000-0000-0000-0000-000000000001/test_workflow_$timestamp.jpg';
        
        expect(constructedUrl, equals(expectedUrl));
        print('✅ URL construction verified: $constructedUrl');

        // 5. Test fetching posts without media (text-only posts)
        print('📄 Testing text-only posts query...');
        final textPostsResponse = await http.get(
          Uri.parse('$baseUrl/rest/v1/posts?select=id,content,media_id,media(*)&media_id=is.null&limit=1'),
          headers: {
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
          },
        );

        expect(textPostsResponse.statusCode, 200);
        final textPosts = jsonDecode(textPostsResponse.body);
        if (textPosts.isNotEmpty) {
          final textPost = textPosts[0];
          expect(textPost['media_id'], isNull);
          expect(textPost['media'], isNull);
          print('✅ Text-only post query verified');
        }

        // 6. Test mixed query (posts with and without media)
        print('🔄 Testing mixed posts query...');
        final mixedResponse = await http.get(
          Uri.parse('$baseUrl/rest/v1/posts?select=id,content,media_id,media(type,name,extension)&limit=5'),
          headers: {
            'apikey': apiKey,
            'Authorization': 'Bearer $apiKey',
          },
        );

        expect(mixedResponse.statusCode, 200);
        final mixedPosts = jsonDecode(mixedResponse.body);
        expect(mixedPosts, isNotEmpty);
        
        // Verify we have both types of posts
        final postsWithMedia = mixedPosts.where((p) => p['media_id'] != null).toList();
        final postsWithoutMedia = mixedPosts.where((p) => p['media_id'] == null).toList();
        
        expect(postsWithMedia, isNotEmpty, reason: 'Should have posts with media');
        print('✅ Mixed query verified: ${postsWithMedia.length} with media, ${postsWithoutMedia.length} without');

        print('🎉 End-to-end media workflow test completed successfully!');
      } catch (e, stackTrace) {
        print('❌ End-to-end test failed: $e');
        print('Stack trace: $stackTrace');
        rethrow;
      }
    });

    test('Test media URL construction for different environments', () async {
      print('🧪 Testing environment-specific URL construction...');

      final testMedia = {
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      // Test localhost (development)
      final localhostUrl = _constructMediaUrl(testMedia, baseUrl: 'http://localhost:8090');
      expect(localhostUrl, equals('http://localhost:8090/storage/v1/object/public/media/user/user123/test_image.jpg'));
      print('✅ Localhost URL: $localhostUrl');

      // Test production-like URL
      final prodUrl = _constructMediaUrl(testMedia, baseUrl: 'https://api.gameflex.com');
      expect(prodUrl, equals('https://api.gameflex.com/storage/v1/object/public/media/user/user123/test_image.jpg'));
      print('✅ Production URL: $prodUrl');

      // Test Android emulator URL
      final emulatorUrl = _constructMediaUrl(testMedia, baseUrl: 'http://********:8090');
      expect(emulatorUrl, equals('http://********:8090/storage/v1/object/public/media/user/user123/test_image.jpg'));
      print('✅ Emulator URL: $emulatorUrl');

      print('🎉 Environment URL tests passed!');
    });

    test('Test performance of media queries', () async {
      print('🧪 Testing media query performance...');

      final stopwatch = Stopwatch()..start();

      // Test complex query with multiple joins
      final response = await http.get(
        Uri.parse('$baseUrl/rest/v1/posts?select=id,content,created_at,media(type,location,name,extension),users(username,display_name)&limit=20'),
        headers: {
          'apikey': apiKey,
          'Authorization': 'Bearer $apiKey',
        },
      );

      stopwatch.stop();

      expect(response.statusCode, 200);
      final data = jsonDecode(response.body) as List;

      print('✅ Complex query with joins completed in ${stopwatch.elapsedMilliseconds}ms');
      print('📊 Retrieved ${data.length} posts with relationships');

      // Verify performance is reasonable (under 100ms for this query)
      expect(stopwatch.elapsedMilliseconds, lessThan(100), 
        reason: 'Query should complete quickly with proper indexing');

      print('🎉 Performance test passed!');
    });
  });
}

/// Construct full URL from media record
String _constructMediaUrl(Map<String, dynamic> media, {String? baseUrl}) {
  final location = media['location'] as String;
  final name = media['name'] as String;
  final extension = media['extension'] as String;
  final channelId = media['channel_id'] as String?;
  final ownerId = media['owner_id'] as String;
  final bucketLocation = media['bucket_location'] as String? ?? 'storage/v1/object';
  final bucketName = media['bucket_name'] as String? ?? 'media';
  final bucketPermission = media['bucket_permission'] as String? ?? 'public';
  
  final host = baseUrl ?? 'http://localhost:8090';
  
  String path;
  if (channelId != null) {
    path = '$location/$ownerId/$channelId/$name.$extension';
  } else {
    path = '$location/$ownerId/$name.$extension';
  }
  
  return '$host/$bucketLocation/$bucketPermission/$bucketName/$path';
}
