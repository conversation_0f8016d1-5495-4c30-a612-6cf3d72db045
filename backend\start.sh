#!/bin/bash

# GameFlex Backend Development Setup Script (Bash)
# This script starts the Supabase development environment

# Function to show help
show_help() {
    echo -e "\033[32mGameFlex Backend Development Setup\033[0m"
    echo -e "\033[33mUsage: ./start.sh\033[0m"
    echo ""
    echo "This script will start the GameFlex development backend using Docker Compose."
    echo "Make sure Docker Desktop is running before executing this script."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

set -e

echo -e "\033[32m🚀 Starting GameFlex Development Backend...\033[0m"

# Check if Docker is running
if docker info > /dev/null 2>&1; then
    echo -e "\033[32m✅ Docker is running\033[0m"
else
    echo -e "\033[31m❌ Docker is not running. Please start Docker Desktop and try again.\033[0m"
    exit 1
fi

# Check if Docker Compose is available
if command -v docker > /dev/null 2>&1 && docker compose version > /dev/null 2>&1; then
    echo -e "\033[32m✅ Docker Compose is available\033[0m"
elif command -v docker-compose > /dev/null 2>&1; then
    echo -e "\033[33m⚠️  Using legacy docker-compose command\033[0m"
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo -e "\033[31m❌ Docker Compose is not available. Please install Docker Desktop with Compose support.\033[0m"
    exit 1
fi

# Set Docker Compose command (prefer new syntax)
if [ -z "$DOCKER_COMPOSE_CMD" ]; then
    DOCKER_COMPOSE_CMD="docker compose"
fi

# Create necessary directories
echo -e "\033[33m📁 Creating necessary directories...\033[0m"
directories=("volumes/storage" "volumes/functions")

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "\033[37m   Created: $dir\033[0m"
    fi
done

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "\033[31m❌ .env file not found. Please create one based on the provided template.\033[0m"
    exit 1
fi

echo -e "\033[32m📋 Environment file found\033[0m"

# Check if hosts file is configured for domain names
echo -e "\033[33m🔍 Checking hosts file configuration...\033[0m"
if ! grep -q "# GameFlex Development - START" /etc/hosts 2>/dev/null; then
    echo -e "\033[33m⚠️  GameFlex domain names not configured in hosts file!\033[0m"
    echo -e "\033[37m   To enable domain names (api.gameflex.local, studio.gameflex.local, etc.):\033[0m"
    echo -e "\033[37m   Run: sudo ./setup-hosts-linux.sh\033[0m"
    echo -e "\033[37m   Or continue with localhost URLs...\033[0m"
    echo ""
else
    echo -e "\033[32m✅ GameFlex domain names configured\033[0m"
fi

# Load environment variables from .env file
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
fi

# Set default ports if not specified
STUDIO_PORT=${STUDIO_PORT:-3000}
KONG_HTTP_PORT=${KONG_HTTP_PORT:-8000}
POSTGRES_PORT=${POSTGRES_PORT:-5432}

# Start the services
echo -e "\033[33m🐳 Starting Docker containers...\033[0m"
# Stop any existing containers first
$DOCKER_COMPOSE_CMD down 2>/dev/null || true

# Start containers with better error handling
if ! $DOCKER_COMPOSE_CMD up -d; then
    echo -e "\033[31m❌ Failed to start Docker containers\033[0m"
    echo -e "\033[33m🔍 Checking for common issues...\033[0m"

    # Show container status
    echo -e "\033[36m📊 Container status:\033[0m"
    $DOCKER_COMPOSE_CMD ps
    exit 1
fi
echo -e "\033[32m✅ Docker containers started\033[0m"

# Wait for services to be ready
echo -e "\033[33m⏳ Waiting for services to be ready...\033[0m"
sleep 15

# Function to check if a URL is responding
test_url() {
    local url="$1"
    local timeout="${2:-5}"
    if curl -f -s --max-time "$timeout" "$url" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check database connectivity
test_database() {
    # First check if the container is running
    if ! $DOCKER_COMPOSE_CMD ps db --format json 2>/dev/null | grep -q '"State":"running"'; then
        return 1
    fi

    # Then check if PostgreSQL is ready
    if $DOCKER_COMPOSE_CMD exec -T db pg_isready -U postgres -h localhost > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Check if database is ready
echo -e "\033[33m🔍 Checking database health...\033[0m"
db_ready=false
attempts=0
max_attempts=15

while [ "$db_ready" = false ] && [ $attempts -lt $max_attempts ]; do
    if test_database; then
        db_ready=true
    else
        echo -e "\033[37m   Waiting for database... (attempt $((attempts + 1))/$max_attempts)\033[0m"
        sleep 2
        attempts=$((attempts + 1))
    fi
done

if [ "$db_ready" = true ]; then
    echo -e "\033[32m✅ Database is ready!\033[0m"
else
    echo -e "\033[31m❌ Database failed to start within expected time\033[0m"
    echo -e "\033[33m🔍 Checking database container status...\033[0m"

    # Show container status
    db_status=$($DOCKER_COMPOSE_CMD ps db --format json 2>/dev/null || echo "")
    if [ -n "$db_status" ]; then
        echo -e "\033[37m   Container info: $db_status\033[0m"
    fi

    echo -e "\033[36m📋 Recent database logs:\033[0m"
    $DOCKER_COMPOSE_CMD logs --tail=20 db
    echo ""
    echo -e "\033[33m💡 Try running: $DOCKER_COMPOSE_CMD logs db\033[0m"
fi

# Function to check Kong API Gateway
test_kong() {
    # Check if Kong container is healthy
    if $DOCKER_COMPOSE_CMD ps kong --format "table {{.State}}" 2>/dev/null | grep -q "running"; then
        # If container is running, try a simple HTTP request
        if curl -f -s --max-time 5 "http://localhost:$KONG_HTTP_PORT/" > /dev/null 2>&1; then
            return 0
        elif curl -s --max-time 5 "http://localhost:$KONG_HTTP_PORT/" > /dev/null 2>&1; then
            # HTTP errors (like 401 Unauthorized) mean Kong is responding
            return 0
        else
            return 1
        fi
    fi
    return 1
}

# Check if Kong is ready
echo -e "\033[33m🔍 Checking API Gateway health...\033[0m"
kong_ready=false
attempts=0

while [ "$kong_ready" = false ] && [ $attempts -lt $max_attempts ]; do
    if test_kong; then
        kong_ready=true
    else
        echo -e "\033[37m   Waiting for API Gateway... (attempt $((attempts + 1))/$max_attempts)\033[0m"
        sleep 2
        attempts=$((attempts + 1))
    fi
done

if [ "$kong_ready" = true ]; then
    echo -e "\033[32m✅ API Gateway is ready!\033[0m"
else
    # Check if container is at least running
    if $DOCKER_COMPOSE_CMD ps kong --format "table {{.State}}" 2>/dev/null | grep -q "running"; then
        echo -e "\033[32m✅ API Gateway container is running (may still be initializing)\033[0m"
    else
        echo -e "\033[33m⚠️  API Gateway is not running\033[0m"
        echo -e "\033[37m   Check logs with: $DOCKER_COMPOSE_CMD logs kong\033[0m"
    fi
fi

# Function to check Studio
test_studio() {
    # Check if Studio container is running
    if $DOCKER_COMPOSE_CMD ps studio --format "table {{.State}}" 2>/dev/null | grep -q "running"; then
        # If container is running, try a simple HTTP request
        if curl -f -s --max-time 3 "http://localhost:$STUDIO_PORT/" > /dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    fi
    return 1
}

# Check if Studio is ready
echo -e "\033[33m🔍 Checking Supabase Studio health...\033[0m"
studio_ready=false
attempts=0

while [ "$studio_ready" = false ] && [ $attempts -lt $max_attempts ]; do
    if test_studio; then
        studio_ready=true
    else
        echo -e "\033[37m   Waiting for Supabase Studio... (attempt $((attempts + 1))/$max_attempts)\033[0m"
        sleep 2
        attempts=$((attempts + 1))
    fi
done

if [ "$studio_ready" = true ]; then
    echo -e "\033[32m✅ Supabase Studio is ready!\033[0m"
else
    # Check if container is at least running
    if $DOCKER_COMPOSE_CMD ps studio --format "table {{.State}}" 2>/dev/null | grep -q "running"; then
        echo -e "\033[32m✅ Supabase Studio container is running (may still be initializing)\033[0m"
    else
        echo -e "\033[33m⚠️  Supabase Studio is not running\033[0m"
        echo -e "\033[37m   Check logs with: $DOCKER_COMPOSE_CMD logs studio\033[0m"
    fi
fi

# Function to check storage initialization
test_storage_init() {
    local storage_status=$($DOCKER_COMPOSE_CMD ps storage-init --format json 2>/dev/null || echo "")
    if [ -z "$storage_status" ]; then
        echo "not_found"
        return
    fi

    # Check if container has exited
    if echo "$storage_status" | grep -q '"State":"exited"'; then
        # Check exit code - look for "Exited (0)" in status
        local status_field=$(echo "$storage_status" | grep -o '"Status":"[^"]*"' | cut -d'"' -f4)
        if echo "$status_field" | grep -q "Exited (0)"; then
            echo "success"
        else
            echo "failed"
        fi
    elif echo "$storage_status" | grep -q '"State":"running"'; then
        echo "running"
    else
        echo "unknown"
    fi
}

# Check storage initialization
echo -e "\033[33m🔍 Checking storage initialization...\033[0m"
storage_init_result=$(test_storage_init)

case "$storage_init_result" in
    "success")
        echo -e "\033[32m✅ Storage initialization completed successfully!\033[0m"
        ;;
    "failed")
        echo -e "\033[31m❌ Storage initialization failed!\033[0m"
        echo -e "\033[33m   Check logs with: $DOCKER_COMPOSE_CMD logs storage-init\033[0m"
        echo -e "\033[37m   Recent storage-init logs:\033[0m"
        $DOCKER_COMPOSE_CMD logs --tail=10 storage-init 2>/dev/null || echo -e "\033[37m   (Could not retrieve logs)\033[0m"
        ;;
    "running")
        echo -e "\033[33m🔄 Storage initialization is still running...\033[0m"
        echo -e "\033[37m   Waiting for completion...\033[0m"

        # Wait longer for storage initialization
        local wait_attempts=0
        local max_wait_attempts=6
        while [ $wait_attempts -lt $max_wait_attempts ]; do
            sleep 10
            storage_init_result=$(test_storage_init)
            wait_attempts=$((wait_attempts + 1))

            if [ "$storage_init_result" = "success" ]; then
                echo -e "\033[32m✅ Storage initialization completed successfully!\033[0m"
                break
            elif [ "$storage_init_result" = "failed" ]; then
                echo -e "\033[31m❌ Storage initialization failed during wait!\033[0m"
                echo -e "\033[33m   Check logs with: $DOCKER_COMPOSE_CMD logs storage-init\033[0m"
                break
            elif [ "$storage_init_result" != "running" ]; then
                echo -e "\033[33m⚠️  Storage initialization status changed unexpectedly: $storage_init_result\033[0m"
                break
            else
                echo -e "\033[37m   Still running... (wait attempt $wait_attempts/$max_wait_attempts)\033[0m"
            fi
        done

        if [ $wait_attempts -eq $max_wait_attempts ] && [ "$storage_init_result" = "running" ]; then
            echo -e "\033[33m⚠️  Storage initialization taking longer than expected\033[0m"
            echo -e "\033[37m   You can check progress with: $DOCKER_COMPOSE_CMD logs -f storage-init\033[0m"
        fi
        ;;
    "not_found")
        echo -e "\033[37mℹ️  Storage initialization container not found (may not be configured)\033[0m"
        ;;
    *)
        echo -e "\033[33m⚠️  Could not check storage initialization status: $storage_init_result\033[0m"
        echo -e "\033[37m   You can check manually with: $DOCKER_COMPOSE_CMD ps storage-init\033[0m"
        ;;
esac

# Check for SQL script issues in database logs
echo -e "\033[33m🔍 Checking for SQL script initialization issues...\033[0m"
db_logs=$($DOCKER_COMPOSE_CMD logs db 2>/dev/null || echo "")

if [ -n "$db_logs" ]; then
    # Look for common SQL error patterns
    error_patterns=(
        "ERROR:"
        "FATAL:"
        "PANIC:"
        "syntax error"
        "relation.*does not exist"
        "column.*does not exist"
        "function.*does not exist"
        "permission denied"
        "duplicate key value"
        "violates.*constraint"
        "invalid input syntax"
        "could not open file"
        "No such file or directory"
        "role.*does not exist"
        "database.*does not exist"
        "authentication failed"
        "connection refused"
        "storage_policies\.sql.*No such file"
        "\\\\i.*No such file"
        "failed to create bucket"
        "storage.*initialization.*failed"
    )

    found_errors=()
    critical_errors=()

    for pattern in "${error_patterns[@]}"; do
        while IFS= read -r line; do
            # Filter out known non-critical messages
            if echo "$line" | grep -qE "(TimescaleDB Background Worker.*due to administrator command|the database system is not accepting connections|database system was shut down|database system is ready to accept connections|autovacuum launcher started|logical replication launcher started|checkpoint starting|checkpoint complete)"; then
                continue
            fi

            found_errors+=("$line")
            # Mark as critical if it's a severe error
            if echo "$line" | grep -qE "(FATAL|PANIC|authentication failed|connection refused|role.*does not exist)"; then
                critical_errors+=("$line")
            fi
        done < <(echo "$db_logs" | grep -iE "$pattern" || true)
    done

    if [ ${#critical_errors[@]} -gt 0 ]; then
        echo -e "\033[31m❌ Critical SQL script errors detected!\033[0m"
        echo -e "\033[31m   Found ${#critical_errors[@]} critical error(s):\033[0m"
        for i in "${!critical_errors[@]}"; do
            if [ $i -lt 5 ]; then
                echo -e "\033[31m   • ${critical_errors[$i]}\033[0m"
            fi
        done
        if [ ${#critical_errors[@]} -gt 5 ]; then
            echo -e "\033[31m   ... and $((${#critical_errors[@]} - 5)) more critical errors\033[0m"
        fi
        echo ""
        echo -e "\033[33m🔧 Recommended actions:\033[0m"
        echo -e "\033[37m   1. Check full logs: $DOCKER_COMPOSE_CMD logs db\033[0m"
        echo -e "\033[37m   2. Stop and restart: ./stop.sh; ./start.sh\033[0m"
        echo -e "\033[37m   3. Check SQL files in volumes/db/ for syntax errors\033[0m"
    elif [ ${#found_errors[@]} -gt 0 ]; then
        echo -e "\033[33m⚠️  SQL script warnings detected\033[0m"
        echo -e "\033[33m   Found ${#found_errors[@]} warning(s) - database may still function\033[0m"
        echo -e "\033[37m   First few warnings:\033[0m"
        for i in "${!found_errors[@]}"; do
            if [ $i -lt 3 ]; then
                echo -e "\033[37m   • ${found_errors[$i]}\033[0m"
            fi
        done
        echo -e "\033[37m   Run '$DOCKER_COMPOSE_CMD logs db' for full details\033[0m"
    else
        echo -e "\033[32m✅ No SQL script errors detected in database logs!\033[0m"
    fi
else
    echo -e "\033[33m⚠️  Could not retrieve database logs for SQL script check\033[0m"
fi

# Check for posts and storage initialization
echo -e "\033[33m📊 Checking posts and storage initialization...\033[0m"
# Check if posts table has data
posts_check=$($DOCKER_COMPOSE_CMD exec -T db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM public.posts;" 2>/dev/null | tr -d ' ' || echo "")
if [[ "$posts_check" =~ ^[0-9]+$ ]]; then
    if [ "$posts_check" -gt 0 ]; then
        echo -e "\033[32m✅ Posts table initialized with $posts_check posts\033[0m"
    else
        echo -e "\033[33m⚠️  Posts table exists but is empty - seed data may not have loaded\033[0m"
    fi
else
    echo -e "\033[31m❌ Failed to check posts table - may not exist or database not ready\033[0m"
fi

# Check if storage bucket exists
bucket_check=$($DOCKER_COMPOSE_CMD exec -T db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM storage.buckets WHERE id = 'media';" 2>/dev/null | tr -d ' ' || echo "")
if [[ "$bucket_check" =~ ^[0-9]+$ ]]; then
    if [ "$bucket_check" -gt 0 ]; then
        echo -e "\033[32m✅ Media storage bucket initialized\033[0m"
    else
        echo -e "\033[33m⚠️  Media storage bucket not found - storage policies may not have loaded\033[0m"
    fi
else
    echo -e "\033[31m❌ Failed to check storage bucket - storage may not be initialized\033[0m"
fi

# Check if users table has data
users_check=$($DOCKER_COMPOSE_CMD exec -T db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM public.users;" 2>/dev/null | tr -d ' ' || echo "")
if [[ "$users_check" =~ ^[0-9]+$ ]]; then
    if [ "$users_check" -gt 0 ]; then
        echo -e "\033[32m✅ Users table initialized with $users_check users\033[0m"
    else
        echo -e "\033[33m⚠️  Users table exists but is empty - seed data may not have loaded\033[0m"
    fi
else
    echo -e "\033[31m❌ Failed to check users table - may not exist or database not ready\033[0m"
fi

# Initialize auth users
echo -e "\033[33m🔐 Initializing authentication users...\033[0m"
if [ -f "scripts/init-auth-users.sh" ]; then
    if ./scripts/init-auth-users.sh; then
        echo -e "\033[32m✅ Authentication users initialized successfully!\033[0m"
    else
        echo -e "\033[33m⚠️  Authentication users initialization completed with warnings\033[0m"
    fi
else
    echo -e "\033[33m⚠️  Authentication users initialization script not found\033[0m"
fi

# Apply storage policies after storage service is ready
if [ -f "apply-storage-policies.sh" ]; then
    echo -e "\033[33m🔧 Applying storage policies...\033[0m"
    if ./apply-storage-policies.sh; then
        echo -e "\033[32m✅ Storage policies applied successfully\033[0m"
    else
        echo -e "\033[33m⚠️  Storage policies application completed with warnings\033[0m"
    fi
else
    echo -e "\033[33m⚠️  Storage policies script not found, skipping storage setup\033[0m"
fi

# Final service status check
echo -e "\033[33m🔍 Final service status check...\033[0m"
all_containers=$($DOCKER_COMPOSE_CMD ps --format json 2>/dev/null || echo "")
if [ -n "$all_containers" ]; then
    total_count=$(echo "$all_containers" | wc -l)
    running_count=$(echo "$all_containers" | grep -c '"State":"running"' || echo "0")

    if [ "$running_count" -eq "$total_count" ] && [ "$total_count" -gt 0 ]; then
        echo -e "\033[32m✅ All $total_count services are running!\033[0m"
    elif [ "$running_count" -gt 0 ]; then
        echo -e "\033[33m⚠️  $running_count of $total_count services are running\033[0m"
    else
        echo -e "\033[31m❌ No services are running\033[0m"
    fi
else
    echo -e "\033[33m⚠️  Could not check final service status\033[0m"
fi

# Display success message and information
echo ""
echo -e "\033[42m\033[30m🎉 GameFlex Development Backend is now running!\033[0m"
echo ""

# Check if domain names are configured and show appropriate URLs
if grep -q "# GameFlex Development - START" /etc/hosts 2>/dev/null; then
    echo -e "\033[36m🎯 GameFlex Services (Domain Names):\033[0m"
    echo -e "\033[36m   📊 Supabase Studio: \033[37mhttp://studio.gameflex.local:$STUDIO_PORT\033[0m"
    echo -e "\033[36m   🔌 API Gateway: \033[37mhttp://api.gameflex.local:$KONG_HTTP_PORT\033[0m"
    echo -e "\033[36m   📧 Mail Interface: \033[37mhttp://localhost:9100\033[0m"
    echo -e "\033[36m   🗄️  Database: \033[37mdb.gameflex.local:$POSTGRES_PORT\033[0m"
else
    echo -e "\033[36m🎯 GameFlex Services (Localhost):\033[0m"
    echo -e "\033[36m   📊 Supabase Studio: \033[37mhttp://localhost:$STUDIO_PORT\033[0m"
    echo -e "\033[36m   🔌 API Gateway: \033[37mhttp://localhost:$KONG_HTTP_PORT\033[0m"
    echo -e "\033[36m   📧 Mail Interface: \033[37mhttp://localhost:9100\033[0m"
    echo -e "\033[36m   🗄️  Database: \033[37mlocalhost:$POSTGRES_PORT\033[0m"
    echo ""
    echo -e "\033[33m💡 To enable domain names, run:\033[0m"
    echo -e "\033[37m   sudo ./setup-hosts-linux.sh\033[0m"
fi
echo ""
echo -e "\033[33m🔑 Development Credentials:\033[0m"
echo -e "\033[37m   📧 <EMAIL> (password: devpassword123)\033[0m"
echo -e "\033[37m   📧 <EMAIL> (password: adminpassword123)\033[0m"
echo -e "\033[37m   📧 <EMAIL> (password: johnpassword123)\033[0m"
echo -e "\033[37m   📧 <EMAIL> (password: janepassword123)\033[0m"
echo -e "\033[37m   📧 <EMAIL> (password: mikepassword123)\033[0m"
echo ""
echo -e "\033[35m🔧 Management Commands:\033[0m"
echo -e "\033[37m   To stop the backend: \033[37m./stop.sh --keep-data\033[0m"
echo -e "\033[37m   To view logs: \033[37m$DOCKER_COMPOSE_CMD logs -f\033[0m"
echo -e "\033[37m   To reset data: \033[37m./stop.sh; ./start.sh\033[0m"
echo -e "\033[37m   To test storage: \033[37m./scripts/test-storage.sh\033[0m"
echo ""

# Display clickable link to Studio
echo -e "\033[36m🌐 Open Supabase Studio: \033[34mhttp://localhost:$STUDIO_PORT\033[0m"
echo -e "\033[37m   (Ctrl+Click to open in browser)\033[0m"
