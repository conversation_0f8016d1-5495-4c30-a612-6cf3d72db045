import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:gameflex_mobile/services/upload_service.dart';
import 'package:gameflex_mobile/services/auth_service.dart';

void main() {
  group('Upload Service Tests with New Media Structure', () {
    setUpAll(() async {
      // Initialize Supabase
      await SupabaseService.initialize();
      print('✅ Supabase initialized for testing');
    });

    test('Test complete upload flow with media table', () async {
      print('🧪 Testing upload service with new media structure...');

      try {
        // Authenticate first
        final authService = AuthService.instance;
        print('🔐 Authenticating test user...');

        final authResult = await authService.signIn(
          email: '<EMAIL>',
          password: 'devpassword123',
        );

        expect(
          authResult.user,
          isNotNull,
          reason: 'Authentication should succeed',
        );
        print('✅ Authentication successful: ${authResult.user?.email}');

        // Create a test image file
        final testImageBytes = _createBlackJPEG();
        final tempDir = Directory.systemTemp;
        final testImageFile = File(
          '${tempDir.path}/upload_test_${DateTime.now().millisecondsSinceEpoch}.jpg',
        );
        await testImageFile.writeAsBytes(testImageBytes);
        print('📸 Created test image: ${testImageFile.path}');

        // Test the upload service
        final uploadService = UploadService();
        final testContent =
            'Upload service test with media structure - ${DateTime.now().millisecondsSinceEpoch}';

        print('📤 Testing upload...');
        final uploadResult = await uploadService.createPost(
          imageFile: testImageFile,
          content: testContent,
        );

        expect(uploadResult, isTrue, reason: 'Upload should succeed');
        print('✅ Upload completed successfully');

        // Verify the post was created with proper media relationship
        print('🔍 Verifying database records...');
        final supabase = SupabaseService.instance.client;

        final posts = await supabase
            .from('posts')
            .select('*, media(*)')
            .eq('content', testContent)
            .limit(1);

        expect(posts, isNotEmpty, reason: 'Post should be found in database');

        final post = posts.first;
        expect(
          post['media_id'],
          isNotNull,
          reason: 'Post should have media_id',
        );
        expect(
          post['media'],
          isNotNull,
          reason: 'Post should have media relationship',
        );

        final media = post['media'];
        expect(media['type'], equals('image'));
        expect(media['location'], equals('user'));
        expect(media['extension'], equals('jpg'));
        expect(media['owner_id'], isNotNull);
        expect(media['bucket_name'], equals('media'));
        expect(media['bucket_permission'], equals('public'));

        print('✅ Database verification successful');
        print('📊 Media ID: ${media['id']}');
        print('📊 Post ID: ${post['id']}');
        print('📊 Media type: ${media['type']}');
        print('📊 Media location: ${media['location']}');
        print('📊 Media extension: ${media['extension']}');

        // Test URL construction using the helper
        final constructedUrl = MediaUrlHelper.constructMediaUrl(media);
        expect(constructedUrl, contains('http://localhost:8090'));
        expect(constructedUrl, contains('storage/v1/object/public/media'));
        expect(constructedUrl, contains('user'));
        expect(constructedUrl, contains(media['owner_id']));
        expect(constructedUrl, contains('.jpg'));

        print('🔗 Constructed URL: $constructedUrl');
        print('✅ URL construction verified');

        // Clean up test file
        if (await testImageFile.exists()) {
          await testImageFile.delete();
          print('🧹 Test file cleaned up');
        }

        print('🎉 Upload service test completed successfully!');
      } catch (e, stackTrace) {
        print('❌ Upload service test failed: $e');
        print('Stack trace: $stackTrace');
        rethrow;
      }
    });

    test('Test MediaUrlHelper for different scenarios', () async {
      print('🧪 Testing MediaUrlHelper...');

      // Test user media (no channel)
      final userMedia = {
        'location': 'user',
        'name': 'test_image_123',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user-456',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final userUrl = MediaUrlHelper.constructMediaUrl(userMedia);
      final expectedUserUrl =
          'http://localhost:8090/storage/v1/object/public/media/user/user-456/test_image_123.jpg';
      expect(userUrl, equals(expectedUserUrl));
      print('✅ User media URL: $userUrl');

      // Test channel media
      final channelMedia = {
        'location': 'minecraft',
        'name': 'castle_build',
        'extension': 'png',
        'channel_id': 'channel-789',
        'owner_id': 'user-456',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final channelUrl = MediaUrlHelper.constructMediaUrl(channelMedia);
      final expectedChannelUrl =
          'http://localhost:8090/storage/v1/object/public/media/minecraft/user-456/channel-789/castle_build.png';
      expect(channelUrl, equals(expectedChannelUrl));
      print('✅ Channel media URL: $channelUrl');

      // Test with custom base URL
      final customUrl = MediaUrlHelper.constructMediaUrl(
        userMedia,
        baseUrl: 'https://api.gameflex.com',
      );
      expect(customUrl, contains('https://api.gameflex.com'));
      print('✅ Custom base URL: $customUrl');

      print('🎉 MediaUrlHelper tests passed!');
    });
  });
}

/// Create a minimal black JPEG image (9:16 aspect ratio)
Uint8List _createBlackJPEG() {
  return Uint8List.fromList([
    0xFF,
    0xD8,
    0xFF,
    0xE0,
    0x00,
    0x10,
    0x4A,
    0x46,
    0x49,
    0x46,
    0x00,
    0x01,
    0x01,
    0x01,
    0x00,
    0x48,
    0x00,
    0x48,
    0x00,
    0x00,
    0xFF,
    0xDB,
    0x00,
    0x43,
    0x00,
    0x08,
    0x06,
    0x06,
    0x07,
    0x06,
    0x05,
    0x08,
    0x07,
    0x07,
    0x07,
    0x09,
    0x09,
    0x08,
    0x0A,
    0x0C,
    0x14,
    0x0D,
    0x0C,
    0x0B,
    0x0B,
    0x0C,
    0x19,
    0x12,
    0x13,
    0x0F,
    0x14,
    0x1D,
    0x1A,
    0x1F,
    0x1E,
    0x1D,
    0x1A,
    0x1C,
    0x1C,
    0x20,
    0x24,
    0x2E,
    0x27,
    0x20,
    0x22,
    0x2C,
    0x23,
    0x1C,
    0x1C,
    0x28,
    0x37,
    0x29,
    0x2C,
    0x30,
    0x31,
    0x34,
    0x34,
    0x34,
    0x1F,
    0x27,
    0x39,
    0x3D,
    0x38,
    0x32,
    0x3C,
    0x2E,
    0x33,
    0x34,
    0x32,
    0xFF,
    0xC0,
    0x00,
    0x11,
    0x08,
    0x01,
    0x40,
    0x00,
    0xB4,
    0x01,
    0x01,
    0x11,
    0x00,
    0x02,
    0x11,
    0x01,
    0x03,
    0x11,
    0x01,
    0xFF,
    0xC4,
    0x00,
    0x14,
    0x00,
    0x01,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x08,
    0xFF,
    0xC4,
    0x00,
    0x14,
    0x10,
    0x01,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0x00,
    0xFF,
    0xDA,
    0x00,
    0x0C,
    0x03,
    0x01,
    0x00,
    0x02,
    0x11,
    0x03,
    0x11,
    0x00,
    0x3F,
    0x00,
    0x00,
    0xFF,
    0xD9,
  ]);
}
