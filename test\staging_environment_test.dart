import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';

// Note: This test file is designed to be run with --dart-define=STAGING=true
// to test staging environment configuration

void main() {
  group('Staging Environment Configuration Tests', () {
    test('Staging environment should use dev.api.gameflex.io URLs', () {
      // This test should be run with: flutter test --dart-define=STAGING=true
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      if (!isStaging) {
        // Skip this test if not running in staging mode
        return;
      }

      final url = SupabaseService.supabaseUrl;

      // Should use staging URL
      expect(
        url.contains('dev.api.gameflex.io'),
        true,
        reason: 'Staging should use dev.api.gameflex.io URLs, got: $url',
      );

      // Should not use local URLs
      expect(
        url.contains('localhost') || url.contains('********'),
        false,
        reason: 'Staging should not use local URLs, got: $url',
      );
    });

    test('Staging URL transformation should work', () {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      if (!isStaging) {
        // Skip this test if not running in staging mode
        return;
      }

      final testUrls = [
        'http://localhost:8090/storage/v1/object/public/uploads/test.jpg',
        'http://********:8090/storage/v1/object/public/uploads/test.jpg',
        'http://gameflex.local:8090/storage/v1/object/public/uploads/test.jpg',
        'http://api.gameflex.local:8090/rest/v1/posts',
      ];

      for (final url in testUrls) {
        final transformed = SupabaseService.transformUrl(url);

        // In staging, should transform to dev.api.gameflex.io
        expect(
          transformed?.contains('dev.api.gameflex.io'),
          true,
          reason:
              'URL should be transformed to dev.api.gameflex.io in staging, got: $transformed',
        );

        // Should not contain local URLs
        expect(
          (transformed?.contains('localhost') ?? false) ||
              (transformed?.contains('********') ?? false),
          false,
          reason:
              'Transformed URL should not contain local domains in staging, got: $transformed',
        );
      }
    });
  });
}
