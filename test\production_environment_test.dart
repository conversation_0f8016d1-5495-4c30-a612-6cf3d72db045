import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';

// Note: This test file is designed to be run with --dart-define=PRODUCTION=true
// to test production environment configuration

void main() {
  group('Production Environment Configuration Tests', () {
    test('Production environment should use gameflex.io URLs', () {
      // This test should be run with: flutter test --dart-define=PRODUCTION=true
      const isProduction = bool.fromEnvironment(
        'PRODUCTION',
        defaultValue: false,
      );

      if (!isProduction) {
        // Skip this test if not running in production mode
        return;
      }

      final url = SupabaseService.supabaseUrl;

      // Should use production URL
      expect(
        url.contains('api.gameflex.io'),
        true,
        reason: 'Production should use gameflex.io URLs, got: $url',
      );

      // Should not use local URLs
      expect(
        url.contains('localhost') || url.contains('********'),
        false,
        reason: 'Production should not use local URLs, got: $url',
      );
    });

    test('Production URL transformation should work', () {
      const isProduction = bool.fromEnvironment(
        'PRODUCTION',
        defaultValue: false,
      );

      if (!isProduction) {
        // Skip this test if not running in production mode
        return;
      }

      final testUrls = [
        'http://localhost:8090/storage/v1/object/public/uploads/test.jpg',
        'http://********:8090/storage/v1/object/public/uploads/test.jpg',
        'http://gameflex.local:8090/storage/v1/object/public/uploads/test.jpg',
        'http://api.gameflex.local:8090/rest/v1/posts',
      ];

      for (final url in testUrls) {
        final transformed = SupabaseService.transformUrl(url);

        // In production, should transform to api.gameflex.io
        expect(
          transformed?.contains('api.gameflex.io'),
          true,
          reason:
              'URL should be transformed to api.gameflex.io in production, got: $transformed',
        );

        // Should not contain local URLs
        expect(
          (transformed?.contains('localhost') ?? false) ||
              (transformed?.contains('********') ?? false),
          false,
          reason:
              'Transformed URL should not contain local domains in production, got: $transformed',
        );
      }
    });
  });
}
