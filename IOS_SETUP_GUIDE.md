# iOS Setup Guide for GameFlex Mobile

This guide explains the iOS-specific configuration for development and production builds.

## Overview

The iOS configuration includes:
- Separate development and production xcconfig files
- Different bundle identifiers for each environment
- Environment-specific app names and display names
- Dedicated schemes for development and production builds

## Files Created

### Configuration Files
- **`ios/Flutter/Development.xcconfig`**: Development environment settings
- **`ios/Flutter/Production.xcconfig`**: Production environment settings

### Schemes
- **`ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner-Development.xcscheme`**: Development scheme
- **`ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner-Production.xcscheme`**: Production scheme

### Modified Files
- **`ios/Runner/Info.plist`**: Updated to use environment variables for app names

## Configuration Details

### Development Configuration (`Development.xcconfig`)
```
PRODUCT_BUNDLE_IDENTIFIER = com.example.gameflexMobile.dev
BUNDLE_DISPLAY_NAME = GameFlex Dev
BUNDLE_NAME = GameFlex Dev
DART_DEFINES = PRODUCTION=false
```

### Production Configuration (`Production.xcconfig`)
```
PRODUCT_BUNDLE_IDENTIFIER = com.example.gameflexMobile
BUNDLE_DISPLAY_NAME = GameFlex
BUNDLE_NAME = GameFlex
DART_DEFINES = PRODUCTION=true
```

## Build Commands

### Development Builds
```powershell
# Debug build (development)
.\build-dev.ps1 -Platform ios

# Release build (development backend)
.\build-dev.ps1 -Platform ios -Release

# Run development
.\run-dev.ps1 -Platform ios
```

### Production Builds
```powershell
# Production build
.\build-prod.ps1 -Platform ios

# Run production (with confirmation)
.\run-prod.ps1 -Platform ios
```

## Manual Xcode Configuration (Optional)

If you prefer to use Xcode directly, you can:

1. Open `ios/Runner.xcworkspace` in Xcode
2. Select the appropriate scheme:
   - **Runner-Development**: For development builds
   - **Runner-Production**: For production builds
3. Build and run as normal

## Environment Variables

The iOS configuration uses these environment variables:

- **`PRODUCT_BUNDLE_IDENTIFIER`**: App bundle identifier
- **`BUNDLE_DISPLAY_NAME`**: App display name (shown on home screen)
- **`BUNDLE_NAME`**: Internal app name
- **`DART_DEFINES`**: Flutter environment definitions

## Code Signing

### Development
- Uses `iPhone Developer` code signing identity
- Set your development team ID in the xcconfig files if needed

### Production
- Uses `iPhone Distribution` code signing identity
- Set your distribution team ID in the xcconfig files for App Store builds

## Testing

### Run iOS-specific tests
```powershell
flutter test test/ios_environment_test.dart
```

### Test with production environment
```powershell
flutter test test/ios_environment_test.dart --dart-define=PRODUCTION=true
```

## Troubleshooting

### Common Issues

1. **Code Signing Errors**
   - Update `DEVELOPMENT_TEAM` in the xcconfig files with your team ID
   - Ensure you have valid certificates and provisioning profiles

2. **Bundle Identifier Conflicts**
   - Development and production use different bundle IDs to allow side-by-side installation
   - Ensure your Apple Developer account supports both bundle IDs

3. **Scheme Not Found**
   - Ensure the scheme files are in the correct location
   - Try cleaning and rebuilding: `flutter clean && flutter pub get`

4. **Environment Variables Not Applied**
   - Verify the xcconfig files are properly formatted
   - Check that Info.plist uses the correct variable names

### Build Clean
```powershell
flutter clean
flutter pub get
```

## Next Steps

1. **Update Team IDs**: Add your Apple Developer team IDs to the xcconfig files
2. **Configure Certificates**: Set up proper code signing certificates
3. **Test Builds**: Verify both development and production builds work
4. **App Store Setup**: Configure App Store Connect for production deployment

## Notes

- Development builds use the `.dev` bundle identifier suffix
- Production builds use the standard bundle identifier
- Both configurations support the same Flutter environment detection
- The schemes automatically use the correct build configurations
- All iOS builds will use localhost for development and api.gameflex.io for production
