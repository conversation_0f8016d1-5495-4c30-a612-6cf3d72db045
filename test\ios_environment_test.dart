import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:flutter/foundation.dart';

void main() {
  group('iOS Environment Configuration Tests', () {
    test('iOS should use localhost for development', () {
      // This test runs in development mode by default (PRODUCTION=false)
      final url = SupabaseService.supabaseUrl;

      // On iOS, should use localhost for development
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        expect(
          url.contains('localhost'),
          true,
          reason: 'iOS development should use localhost URLs, got: $url',
        );

        // Should not use production URL
        expect(
          url.contains('gameflex.io'),
          false,
          reason: 'iOS development should not use production URLs, got: $url',
        );
      }
    });

    test('iOS URL transformation should work based on environment', () {
      const isProduction = bool.fromEnvironment(
        'PRODUCTION',
        defaultValue: false,
      );

      // Test URL transformations for iOS
      final testUrls = [
        'http://localhost:8090/storage/v1/object/public/uploads/test.jpg',
        'http://gameflex.local:8090/storage/v1/object/public/uploads/test.jpg',
        'http://api.gameflex.local:8090/rest/v1/posts',
      ];

      for (final url in testUrls) {
        final transformed = SupabaseService.transformUrl(url);

        if (isProduction) {
          // In production, URLs should be transformed to api.gameflex.io
          expect(
            transformed?.contains('api.gameflex.io'),
            true,
            reason:
                'iOS URL should use production addresses in production, got: $transformed',
          );
        } else {
          // In development on iOS, URLs should remain as localhost or be transformed to ********
          expect(
            (transformed?.contains('localhost') ?? false) ||
                (transformed?.contains('********') ?? false),
            true,
            reason:
                'iOS URL should use local addresses in development, got: $transformed',
          );

          // Should not contain production URLs in development
          expect(
            transformed?.contains('gameflex.io'),
            false,
            reason:
                'Transformed URL should not contain production domain in development, got: $transformed',
          );
        }
      }
    });

    test('Environment detection should work correctly', () {
      const isProduction = bool.fromEnvironment(
        'PRODUCTION',
        defaultValue: false,
      );

      // Verify the service reflects the current environment
      final url = SupabaseService.supabaseUrl;

      if (isProduction) {
        // In production mode, should use production URLs
        expect(
          url.contains('gameflex.io'),
          true,
          reason: 'Production mode should use production URLs, got: $url',
        );
      } else {
        // In development mode, should use local URLs
        expect(
          url.contains('gameflex.io'),
          false,
          reason: 'Development mode should not use production URLs, got: $url',
        );
      }
    });

    test('Anonymous key should be available for iOS', () {
      final anonKey = SupabaseService.supabaseAnonKey;

      expect(
        anonKey.isNotEmpty,
        true,
        reason: 'Anonymous key should not be empty',
      );
      expect(
        anonKey.startsWith('eyJ'),
        true,
        reason: 'Anonymous key should be a valid JWT token',
      );
    });
  });
}
