class ImageResolution {
  final String name;
  final int width;
  final int height;
  final double aspectRatio;

  const ImageResolution({
    required this.name,
    required this.width,
    required this.height,
  }) : aspectRatio = width / height;

  @override
  String toString() => '$name ($width x $height)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageResolution &&
          runtimeType == other.runtimeType &&
          width == other.width &&
          height == other.height;

  @override
  int get hashCode => width.hashCode ^ height.hashCode;

  // Fixed 9:16 resolution for all uploads (portrait)
  static const ImageResolution fixedResolution = ImageResolution(
    name: '9:16',
    width: 1080,
    height: 1920,
  );

  // Always return the fixed 16:9 resolution
  static ImageResolution getBestFit(int imageWidth, int imageHeight) {
    return fixedResolution;
  }
}
