import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/providers/user_profile_provider.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/screens/user_profile_screen.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';

void main() {
  group('User Profile Tests', () {
    testWidgets('UserProfileScreen displays loading state initially', (WidgetTester tester) async {
      // Create providers
      final userProfileProvider = UserProfileProvider();
      final authProvider = AuthProvider();

      // Build the widget
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider.value(value: userProfileProvider),
            ChangeNotifierProvider.value(value: authProvider),
          ],
          child: MaterialApp(
            theme: AppTheme.darkTheme,
            home: const UserProfileScreen(
              userId: 'test-user-id',
              username: 'testuser',
            ),
          ),
        ),
      );

      // Verify loading state is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('testuser'), findsOneWidget);
    });

    testWidgets('UserProfileProvider initializes correctly', (WidgetTester tester) async {
      final provider = UserProfileProvider();

      // Test initial state
      expect(provider.status, UserProfileStatus.initial);
      expect(provider.userProfile, isNull);
      expect(provider.userPosts, isEmpty);
      expect(provider.userStats, isEmpty);
      expect(provider.hasMorePosts, isTrue);
      expect(provider.isLoading, isFalse);
    });

    testWidgets('UserProfileProvider clears data correctly', (WidgetTester tester) async {
      final provider = UserProfileProvider();

      // Clear profile data
      provider.clearProfile();

      // Verify state is reset
      expect(provider.status, UserProfileStatus.initial);
      expect(provider.userProfile, isNull);
      expect(provider.userPosts, isEmpty);
      expect(provider.userStats, isEmpty);
      expect(provider.hasMorePosts, isTrue);
    });
  });
}
